"""
Database Cleanup Service
Handles identification and removal of inactive users from the bot database
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError, BadRequest, Forbidden, ChatMigrated

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class DatabaseCleanupService:
    """Service for cleaning up inactive users from the database"""
    
    def __init__(self, bot: Bot = None):
        self.bot = bot or Bot(settings.BOT_TOKEN)
        self.rate_limit_delay = 1.0 / 30  # More conservative: 30 requests per second

        # Optimizations for large databases - more conservative settings
        self.batch_size = 100  # Smaller batch size for stability
        self.memory_cleanup_interval = 500  # Clean up memory more frequently
        self.progress_report_interval = 25  # Report progress more frequently
        self.max_concurrent_requests = 5  # Reduce concurrent requests for stability
        
    async def cleanup_inactive_users(self, progress_callback=None) -> Dict[str, Any]:
        """
        Main cleanup function that identifies and removes inactive users
        
        Args:
            progress_callback: Optional callback function to report progress
            
        Returns:
            Dict with cleanup statistics and results
        """
        try:
            logger.info("Starting database cleanup process")
            
            # Get all users from database
            users_collection = await get_collection(COLLECTIONS['users'])
            total_users = await users_collection.count_documents({})
            
            if total_users == 0:
                return {
                    'success': True,
                    'total_users': 0,
                    'processed_users': 0,
                    'removed_users': 0,
                    'blocked_users': 0,
                    'deleted_accounts': 0,
                    'inaccessible_users': 0,
                    'errors': 0,
                    'message': 'No users found in database'
                }
            
            # Initialize statistics
            stats = {
                'total_users': total_users,
                'processed_users': 0,
                'removed_users': 0,
                'blocked_users': 0,
                'deleted_accounts': 0,
                'inaccessible_users': 0,
                'errors': 0,
                'inactive_user_ids': []
            }
            
            # Process users in optimized batches for large databases
            cursor = users_collection.find({}, {'user_id': 1, 'first_name': 1, 'username': 1})

            batch = []
            processed_count = 0
            last_progress_report = 0

            try:
                async for user in cursor:
                    batch.append(user)

                    if len(batch) >= self.batch_size:
                        try:
                            await self._process_user_batch_optimized(batch, stats, progress_callback)
                            processed_count += len(batch)
                            batch = []

                            # Memory cleanup for large datasets
                            if processed_count % self.memory_cleanup_interval == 0:
                                import gc
                                gc.collect()
                                logger.info(f"Memory cleanup performed after {processed_count} users")

                            # Progress logging for monitoring
                            if processed_count - last_progress_report >= 100:
                                logger.info(f"Processed {processed_count} users so far...")
                                last_progress_report = processed_count

                            # Conservative delay between batches for stability
                            await asyncio.sleep(0.5)

                        except Exception as e:
                            logger.error(f"Error processing batch at {processed_count}: {e}")
                            # Continue with next batch instead of failing completely
                            batch = []
                            stats['errors'] += len(batch)

                # Process remaining users
                if batch:
                    try:
                        await self._process_user_batch_optimized(batch, stats, progress_callback)
                        logger.info(f"Processed final batch of {len(batch)} users")
                    except Exception as e:
                        logger.error(f"Error processing final batch: {e}")
                        stats['errors'] += len(batch)

            except Exception as e:
                logger.error(f"Error during user iteration: {e}")
                raise
            
            # Remove inactive users from database
            if stats['inactive_user_ids']:
                removed_count = await self._remove_inactive_users(stats['inactive_user_ids'])
                stats['removed_users'] = removed_count
            
            # Calculate final statistics
            stats['success'] = True
            stats['message'] = f"Cleanup completed successfully. Removed {stats['removed_users']} inactive users."
            
            logger.info(f"Database cleanup completed: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error during database cleanup: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f"Cleanup failed: {str(e)}"
            }
    
    async def _process_user_batch_optimized(self, batch: List[Dict], stats: Dict, progress_callback=None):
        """Process a batch of users with improved stability for large databases"""
        try:
            # Use more conservative concurrent processing
            semaphore = asyncio.Semaphore(self.max_concurrent_requests)

            async def process_single_user(user):
                async with semaphore:
                    user_id = user['user_id']
                    max_retries = 3
                    retry_delay = 1.0

                    for attempt in range(max_retries):
                        try:
                            # Check user status with Telegram API
                            status = await self._check_user_status(user_id)

                            # Update stats atomically
                            stats['processed_users'] += 1

                            if status['is_inactive']:
                                stats['inactive_user_ids'].append(user_id)

                                if status['reason'] == 'blocked':
                                    stats['blocked_users'] += 1
                                elif status['reason'] == 'deleted':
                                    stats['deleted_accounts'] += 1
                                else:
                                    stats['inaccessible_users'] += 1

                            # Rate limiting with jitter to avoid thundering herd
                            jitter = 0.1 * (0.5 - asyncio.get_event_loop().time() % 1)
                            await asyncio.sleep(self.rate_limit_delay + jitter)

                            return True

                        except Exception as e:
                            if attempt < max_retries - 1:
                                logger.warning(f"Error processing user {user_id} (attempt {attempt + 1}): {e}")
                                await asyncio.sleep(retry_delay * (attempt + 1))
                                continue
                            else:
                                logger.error(f"Failed to process user {user_id} after {max_retries} attempts: {e}")
                                stats['errors'] += 1
                                return False

            # Process batch with error handling
            tasks = [process_single_user(user) for user in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Count successful vs failed operations
            successful = sum(1 for r in results if r is True)
            failed = len(batch) - successful

            if failed > 0:
                logger.warning(f"Batch processing: {successful} successful, {failed} failed")

            # Report progress more frequently for better monitoring
            if progress_callback and stats['processed_users'] % self.progress_report_interval == 0:
                await progress_callback(stats)

        except Exception as e:
            logger.error(f"Error processing user batch: {e}")
            stats['errors'] += len(batch)

    # Keep the original method for backward compatibility
    async def _process_user_batch(self, batch: List[Dict], stats: Dict, progress_callback=None):
        """Legacy method - redirects to optimized version"""
        await self._process_user_batch_optimized(batch, stats, progress_callback)
    
    async def _check_user_status(self, user_id: int) -> Dict[str, Any]:
        """
        Check if a user is still active by attempting to get their chat info
        
        Args:
            user_id: Telegram user ID to check
            
        Returns:
            Dict with status information
        """
        try:
            # Try to get chat information
            chat = await self.bot.get_chat(user_id)
            
            # If we get here, the user is accessible
            return {
                'is_inactive': False,
                'reason': None,
                'chat_info': {
                    'id': chat.id,
                    'type': chat.type,
                    'first_name': getattr(chat, 'first_name', None),
                    'username': getattr(chat, 'username', None)
                }
            }
            
        except Forbidden as e:
            # User has blocked the bot
            logger.debug(f"User {user_id} has blocked the bot: {e}")
            return {
                'is_inactive': True,
                'reason': 'blocked',
                'error': str(e)
            }
            
        except BadRequest as e:
            error_msg = str(e).lower()
            
            if 'chat not found' in error_msg or 'user not found' in error_msg:
                # User account has been deleted
                logger.debug(f"User {user_id} account deleted: {e}")
                return {
                    'is_inactive': True,
                    'reason': 'deleted',
                    'error': str(e)
                }
            else:
                # Other bad request errors - consider as inaccessible
                logger.debug(f"User {user_id} inaccessible: {e}")
                return {
                    'is_inactive': True,
                    'reason': 'inaccessible',
                    'error': str(e)
                }
                
        except ChatMigrated as e:
            # Chat has been migrated (shouldn't happen for private chats, but handle it)
            logger.debug(f"User {user_id} chat migrated: {e}")
            return {
                'is_inactive': False,  # Don't remove migrated chats
                'reason': 'migrated',
                'error': str(e)
            }
            
        except TelegramError as e:
            # Handle specific Telegram errors
            error_msg = str(e).lower()

            if 'too many requests' in error_msg or 'rate limit' in error_msg:
                # Rate limit hit - wait longer and don't remove user
                logger.warning(f"Rate limit hit for user {user_id}: {e}")
                await asyncio.sleep(5.0)  # Wait 5 seconds on rate limit
                return {
                    'is_inactive': False,
                    'reason': 'rate_limited',
                    'error': str(e)
                }
            elif 'network' in error_msg or 'timeout' in error_msg:
                # Network issues - don't remove user
                logger.warning(f"Network error checking user {user_id}: {e}")
                return {
                    'is_inactive': False,
                    'reason': 'network_error',
                    'error': str(e)
                }
            else:
                # Other Telegram errors - don't remove user to be safe
                logger.warning(f"Telegram error checking user {user_id}: {e}")
                return {
                    'is_inactive': False,
                    'reason': 'telegram_error',
                    'error': str(e)
                }

        except Exception as e:
            # Unexpected errors - don't remove user to be safe
            logger.error(f"Unexpected error checking user {user_id}: {e}")
            return {
                'is_inactive': False,
                'reason': 'unexpected_error',
                'error': str(e)
            }

    async def _remove_inactive_users(self, inactive_user_ids: List[int]) -> int:
        """
        Remove inactive users from the database with optimized bulk operations

        Args:
            inactive_user_ids: List of user IDs to remove

        Returns:
            Number of users actually removed
        """
        try:
            if not inactive_user_ids:
                return 0

            logger.info(f"Removing {len(inactive_user_ids)} inactive users from database")

            # Get collections
            users_collection = await get_collection(COLLECTIONS['users'])
            task_submissions_collection = await get_collection(COLLECTIONS['task_submissions'])
            sessions_collection = await get_collection(COLLECTIONS['sessions'])
            rate_limits_collection = await get_collection(COLLECTIONS['rate_limits'])

            # Use bulk operations for better performance with large datasets
            try:
                # Remove users in bulk
                user_result = await users_collection.delete_many({"user_id": {"$in": inactive_user_ids}})
                removed_count = user_result.deleted_count

                if removed_count > 0:
                    # Clean up related data in bulk
                    await self._cleanup_user_related_data_bulk(
                        inactive_user_ids,
                        task_submissions_collection,
                        sessions_collection,
                        rate_limits_collection
                    )

                    logger.info(f"Successfully removed {removed_count} inactive users using bulk operations")

                return removed_count

            except Exception as e:
                logger.warning(f"Bulk removal failed, falling back to individual removal: {e}")

                # Fallback to individual removal
                removed_count = 0
                for user_id in inactive_user_ids:
                    try:
                        user_result = await users_collection.delete_one({"user_id": user_id})

                        if user_result.deleted_count > 0:
                            removed_count += 1

                            # Clean up related data
                            await self._cleanup_user_related_data_individual(user_id)

                            logger.debug(f"Removed inactive user {user_id} and related data")

                    except Exception as e:
                        logger.error(f"Error removing user {user_id}: {e}")
                        continue

                logger.info(f"Successfully removed {removed_count} inactive users using individual operations")
                return removed_count

        except Exception as e:
            logger.error(f"Error in _remove_inactive_users: {e}")
            return 0

    async def _cleanup_user_related_data_bulk(
        self,
        user_ids: List[int],
        task_submissions_collection,
        sessions_collection,
        rate_limits_collection
    ):
        """Clean up data related to removed users using bulk operations"""
        try:
            # Remove user task submissions in bulk
            await task_submissions_collection.delete_many({"user_id": {"$in": user_ids}})

            # Remove user session data in bulk
            await sessions_collection.delete_many({"user_id": {"$in": user_ids}})

            # Remove user rate limit records in bulk
            await rate_limits_collection.delete_many({"user_id": {"$in": user_ids}})

            logger.debug(f"Cleaned up related data for {len(user_ids)} users using bulk operations")

            # Note: We intentionally keep withdrawal records for audit purposes
            # and referral relationships to maintain data integrity

        except Exception as e:
            logger.error(f"Error cleaning up related data in bulk: {e}")

    async def _cleanup_user_related_data_individual(self, user_id: int):
        """Clean up data related to a single removed user"""
        try:
            # Get collections
            task_submissions_collection = await get_collection(COLLECTIONS['task_submissions'])
            sessions_collection = await get_collection(COLLECTIONS['sessions'])
            rate_limits_collection = await get_collection(COLLECTIONS['rate_limits'])

            # Remove user's task submissions
            await task_submissions_collection.delete_many({"user_id": user_id})

            # Remove user's session data
            await sessions_collection.delete_many({"user_id": user_id})

            # Remove user's rate limit records
            await rate_limits_collection.delete_many({"user_id": user_id})

            # Note: We intentionally keep withdrawal records for audit purposes
            # and referral relationships to maintain data integrity

        except Exception as e:
            logger.error(f"Error cleaning up related data for user {user_id}: {e}")

    # Keep the original method for backward compatibility
    async def _cleanup_user_related_data(
        self,
        user_id: int,
        withdrawals_collection,
        task_submissions_collection,
        sessions_collection,
        rate_limits_collection
    ):
        """Legacy method - clean up data related to a removed user"""
        try:
            # Remove user's task submissions
            await task_submissions_collection.delete_many({"user_id": user_id})

            # Remove user's session data
            await sessions_collection.delete_many({"user_id": user_id})

            # Remove user's rate limit records
            await rate_limits_collection.delete_many({"user_id": user_id})

            # Note: We intentionally keep withdrawal records for audit purposes
            # and referral relationships to maintain data integrity

        except Exception as e:
            logger.error(f"Error cleaning up related data for user {user_id}: {e}")

    async def get_cleanup_preview(self) -> Dict[str, Any]:
        """
        Get a preview of what would be cleaned up without actually removing users

        Returns:
            Dict with preview statistics
        """
        try:
            logger.info("Generating cleanup preview")

            # Get all users from database
            users_collection = await get_collection(COLLECTIONS['users'])
            total_users = await users_collection.count_documents({})

            if total_users == 0:
                return {
                    'success': True,
                    'total_users': 0,
                    'estimated_removals': 0,
                    'message': 'No users found in database'
                }

            # Sample a subset of users for preview (max 100)
            sample_size = min(100, total_users)
            cursor = users_collection.aggregate([
                {"$sample": {"size": sample_size}},
                {"$project": {"user_id": 1, "first_name": 1, "username": 1}}
            ])

            sample_users = await cursor.to_list(length=None)

            # Check status of sample users
            inactive_count = 0
            checked_count = 0

            for user in sample_users:
                try:
                    status = await self._check_user_status(user['user_id'])
                    checked_count += 1

                    if status['is_inactive']:
                        inactive_count += 1

                    # Rate limiting for preview
                    await asyncio.sleep(self.rate_limit_delay)

                except Exception as e:
                    logger.error(f"Error checking user {user['user_id']} in preview: {e}")
                    continue

            # Estimate total removals based on sample
            if checked_count > 0:
                inactive_percentage = inactive_count / checked_count
                estimated_removals = int(total_users * inactive_percentage)
            else:
                estimated_removals = 0

            return {
                'success': True,
                'total_users': total_users,
                'sample_size': checked_count,
                'sample_inactive': inactive_count,
                'estimated_removals': estimated_removals,
                'estimated_percentage': round(inactive_percentage * 100, 2) if checked_count > 0 else 0,
                'message': f"Preview completed. Estimated {estimated_removals} users ({round(inactive_percentage * 100, 2) if checked_count > 0 else 0}%) would be removed."
            }

        except Exception as e:
            logger.error(f"Error generating cleanup preview: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': f"Preview failed: {str(e)}"
            }

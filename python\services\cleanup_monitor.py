"""
Cleanup Process Monitor
Monitors background cleanup processes and sends completion notifications
"""

import asyncio
import json
import logging
import time
from pathlib import Path
from typing import Dict, Set

from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from config.settings import settings
from services.background_cleanup_manager import BackgroundCleanupManager
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class CleanupMonitor:
    """Monitors cleanup processes and sends notifications"""
    
    def __init__(self, bot: Bot = None):
        self.bot = bot or Bo<PERSON>(settings.BOT_TOKEN)
        self.background_manager = BackgroundCleanupManager(self.bot)
        self.monitored_processes: Set[str] = set()
        self.check_interval = 30  # Check every 30 seconds
        self.running = False
    
    async def start_monitoring(self):
        """Start the monitoring loop"""
        if self.running:
            logger.warning("Cleanup monitor is already running")
            return
        
        self.running = True
        logger.info("Starting cleanup process monitor")
        
        try:
            while self.running:
                await self._check_processes()
                await asyncio.sleep(self.check_interval)
        except Exception as e:
            logger.error(f"Error in cleanup monitor: {e}")
        finally:
            self.running = False
            logger.info("Cleanup process monitor stopped")
    
    async def stop_monitoring(self):
        """Stop the monitoring loop"""
        self.running = False
        logger.info("Stopping cleanup process monitor")
    
    async def _check_processes(self):
        """Check all cleanup processes for completion"""
        try:
            # Find all status files
            status_files = list(Path(".").glob("cleanup_status_*.json"))
            
            for status_file in status_files:
                try:
                    cleanup_id = status_file.stem.replace("cleanup_status_", "")
                    
                    # Read status
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status = json.load(f)
                    
                    # Check if this is a newly completed process
                    if self._is_newly_completed(cleanup_id, status):
                        await self._handle_completion(cleanup_id, status)
                    
                    # Clean up old completed processes from monitoring
                    if status.get('status') in ['completed', 'failed', 'stopped']:
                        self.monitored_processes.discard(cleanup_id)
                    else:
                        self.monitored_processes.add(cleanup_id)
                        
                except Exception as e:
                    logger.debug(f"Error checking status file {status_file}: {e}")
                    continue
            
            # Clean up old status files
            await self.background_manager.cleanup_old_status_files()
            
        except Exception as e:
            logger.error(f"Error checking cleanup processes: {e}")
    
    def _is_newly_completed(self, cleanup_id: str, status: Dict) -> bool:
        """Check if a process has newly completed"""
        try:
            current_status = status.get('status')
            
            # Check if process is completed and we haven't notified yet
            if current_status in ['completed', 'failed', 'stopped']:
                # Check if we've already processed this completion
                notification_sent = status.get('notification_sent', False)
                
                if not notification_sent:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking completion status for {cleanup_id}: {e}")
            return False
    
    async def _handle_completion(self, cleanup_id: str, status: Dict):
        """Handle a newly completed cleanup process"""
        try:
            admin_id = status.get('admin_id')
            
            if not admin_id:
                logger.warning(f"No admin ID found for completed cleanup {cleanup_id}")
                return
            
            # Send completion notification
            await self.background_manager.send_completion_notification(admin_id, status)
            
            # Mark notification as sent
            await self._mark_notification_sent(cleanup_id)
            
            logger.info(f"Handled completion for cleanup process {cleanup_id}")
            
        except Exception as e:
            logger.error(f"Error handling completion for {cleanup_id}: {e}")
    
    async def _mark_notification_sent(self, cleanup_id: str):
        """Mark that notification has been sent for a cleanup process"""
        try:
            status_file = Path(f"cleanup_status_{cleanup_id}.json")
            
            if not status_file.exists():
                return
            
            # Read current status
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
            
            # Mark notification as sent
            status['notification_sent'] = True
            status['notification_sent_at'] = get_current_timestamp()
            
            # Write back
            with open(status_file, 'w', encoding='utf-8') as f:
                json.dump(status, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Error marking notification sent for {cleanup_id}: {e}")
    
    async def get_active_processes(self) -> Dict[str, Dict]:
        """Get information about all active cleanup processes"""
        try:
            active_processes = {}
            status_files = list(Path(".").glob("cleanup_status_*.json"))
            
            for status_file in status_files:
                try:
                    cleanup_id = status_file.stem.replace("cleanup_status_", "")
                    
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status = json.load(f)
                    
                    # Only include active processes
                    if status.get('status') not in ['completed', 'failed', 'stopped']:
                        active_processes[cleanup_id] = status
                        
                except Exception as e:
                    logger.debug(f"Error reading status file {status_file}: {e}")
                    continue
            
            return active_processes
            
        except Exception as e:
            logger.error(f"Error getting active processes: {e}")
            return {}
    
    async def get_process_statistics(self) -> Dict:
        """Get statistics about cleanup processes"""
        try:
            stats = {
                'active_processes': 0,
                'completed_today': 0,
                'failed_today': 0,
                'total_users_processed_today': 0,
                'total_users_removed_today': 0
            }
            
            current_time = time.time()
            today_start = current_time - (24 * 3600)  # 24 hours ago
            
            status_files = list(Path(".").glob("cleanup_status_*.json"))
            
            for status_file in status_files:
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status = json.load(f)
                    
                    current_status = status.get('status')
                    started_at = status.get('started_at', 0)
                    
                    # Count active processes
                    if current_status not in ['completed', 'failed', 'stopped']:
                        stats['active_processes'] += 1
                    
                    # Count today's completed/failed processes
                    if started_at >= today_start:
                        if current_status == 'completed':
                            stats['completed_today'] += 1
                            
                            # Add to totals
                            result = status.get('result', {})
                            stats['total_users_processed_today'] += result.get('processed_users', 0)
                            stats['total_users_removed_today'] += result.get('removed_users', 0)
                            
                        elif current_status == 'failed':
                            stats['failed_today'] += 1
                            
                except Exception as e:
                    logger.debug(f"Error processing status file {status_file}: {e}")
                    continue
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting process statistics: {e}")
            return {}

# Global monitor instance
cleanup_monitor = CleanupMonitor()

async def start_cleanup_monitor():
    """Start the global cleanup monitor"""
    await cleanup_monitor.start_monitoring()

async def stop_cleanup_monitor():
    """Stop the global cleanup monitor"""
    await cleanup_monitor.stop_monitoring()

"""
Admin command handlers - Original Structure with Enhanced Functionality
Maintains identical functionality to PHP version with improved performance
"""

import logging
from typing import Optional, Dict, Any, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import BadRequest, TimedOut, NetworkError

from services.admin_service import AdminService
from services.user_service import UserService
from services.database_cleanup_service import DatabaseCleanupService
from services.background_cleanup_manager import BackgroundCleanupManager
from utils.helpers import is_admin_async, get_rank_emoji, get_current_date, get_current_timestamp
from utils.constants import ADMIN_ACCESS_DENIED

logger = logging.getLogger(__name__)

class AdminHandlers:
    """Handles admin-related commands and interactions - Original Structure"""
    
    def __init__(self, bot=None):
        self.admin_service = AdminService()
        self.user_service = UserService()
        self.cleanup_service = DatabaseCleanupService(bot)
        self.background_cleanup_manager = BackgroundCleanupManager(bot)

    async def _safe_edit_message(self, query, message: str, keyboard=None, context=None):
        """Safely edit message with media-to-text transition handling"""
        try:
            # Check if the current message has media (photo, video, document, etc.)
            current_message = query.message
            has_media = (
                current_message.photo or
                current_message.video or
                current_message.document or
                current_message.animation or
                current_message.audio or
                current_message.voice or
                current_message.video_note or
                current_message.sticker
            )

            if has_media:
                # Message contains media, we need to send a new text message
                logger.info("Admin message contains media, sending new text message instead of editing")
                chat_id = current_message.chat_id

                # Try to delete the current message first
                try:
                    await current_message.delete()
                except Exception as delete_error:
                    logger.warning(f"Could not delete admin media message: {delete_error}")
                    # Continue anyway, we'll send a new message

                # Send new text message
                if context and hasattr(context, 'bot'):
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                else:
                    # Fallback: try to get bot from query
                    try:
                        from telegram import Bot
                        from config.settings import settings
                        bot = Bot(settings.BOT_TOKEN)
                        await bot.send_message(
                            chat_id=chat_id,
                            text=message,
                            reply_markup=keyboard,
                            parse_mode='HTML'
                        )
                    except Exception as bot_error:
                        logger.error(f"Error creating bot instance in admin handler: {bot_error}")
                        await query.answer("❌ Failed to display message. Please try again.", show_alert=True)
            else:
                # Message is text-only, safe to edit
                await query.edit_message_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            error_message = str(e).lower()
            logger.error(f"Error in admin safe edit message: {e}")

            # Check if the error is specifically about editing media messages
            if "there is no text in the message to edit" in error_message or "message can't be edited" in error_message:
                logger.info("Detected admin media message edit error, falling back to new message")
                try:
                    chat_id = query.message.chat_id
                    # Delete current message if possible
                    try:
                        await query.message.delete()
                    except:
                        pass  # Ignore delete errors

                    # Send new message
                    if context and hasattr(context, 'bot'):
                        await context.bot.send_message(
                            chat_id=chat_id,
                            text=message,
                            reply_markup=keyboard,
                            parse_mode='HTML'
                        )
                    else:
                        # Fallback: try to get bot from query
                        try:
                            from telegram import Bot
                            from config.settings import settings
                            bot = Bot(settings.BOT_TOKEN)
                            await bot.send_message(
                                chat_id=chat_id,
                                text=message,
                                reply_markup=keyboard,
                                parse_mode='HTML'
                            )
                        except Exception as bot_error:
                            logger.error(f"Error in admin fallback bot creation: {bot_error}")
                            await query.answer("❌ Failed to display message. Please try again.", show_alert=True)

                except Exception as fallback_error:
                    logger.error(f"Error in admin media message fallback: {fallback_error}")
                    await query.answer("❌ Failed to display message. Please try again.", show_alert=True)
            else:
                # Other types of errors
                await query.answer("❌ Failed to display message. Please try again.", show_alert=True)

    async def handle_admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /admin command and 'admin' callback - NEW Enhanced Admin Panel"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                logger.debug(f"User {user_id} denied access to /admin command - not an admin")
                return

            # Get new enhanced admin dashboard
            admin_menu = await self._get_new_admin_dashboard()
            admin_message = await self._get_new_admin_message()

            # Check if this is a callback query (navigation) or text command
            if update.callback_query:
                # This is navigation from a "Back to Admin Panel" button
                await update.callback_query.edit_message_text(
                    admin_message,
                    reply_markup=admin_menu,
                    parse_mode='HTML'
                )
            else:
                # This is the /admin text command
                await update.message.reply_text(
                    admin_message,
                    reply_markup=admin_menu,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_admin_command: {e}")

            # Handle error response based on update type
            try:
                if update.callback_query:
                    await update.callback_query.answer("❌ Something went wrong. Please try again later.", show_alert=True)
                elif update.message:
                    await update.message.reply_text(
                        "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                        parse_mode='HTML'
                    )
                else:
                    logger.error("Both callback_query and message are None in handle_admin_command")
            except Exception as error_e:
                logger.error(f"Error in error handling for handle_admin_command: {error_e}")

    # ==================== OLD ADMIN PANEL METHODS (COMMENTED OUT) ====================
    # These methods are preserved for reference but not used in the new system

    # async def _get_original_admin_message(self) -> str:
    #     """Get original admin dashboard message matching PHP version"""
    #     try:
    #         from services.admin_service import AdminService
    #         admin_service = AdminService()
    #         admin_settings = await admin_service.get_admin_settings()

    #         bot_username = admin_settings.get('bot_username', 'Unknown')
    #         main_channel = admin_settings.get('main_channel', 'Not set')
    #         private_logs_channel = admin_settings.get('private_logs_channel', 'Not set')
    #         maintenance_status = admin_settings.get('maintenance_status', 'Off')
    #         otp_api_key = admin_settings.get('otp_website_api_key', 'Not set')
    #         per_refer_amount = admin_settings.get('per_refer_amount', '0')
    #         joining_bonus_amount = admin_settings.get('joining_bonus_amount', '0')

    #         # Get referral and bonus ranges
    #         per_refer_range = admin_settings.get('per_refer_amount_range', '1-3')
    #         joining_bonus_range = admin_settings.get('joining_bonus_amount_range', '49-55')

    #         # Format ranges for display
    #         referral_range_display = f"₹{per_refer_range}"
    #         bonus_range_display = f"₹{joining_bonus_range}"

    #         # Create the original admin message format
    #         message = f"<b>👋 Hello bro, welcome to the admin panel of @{bot_username}.\n\n"
    #         message += f"🏘️ Main channel : @{main_channel}\n"
    #         message += f"🤫 Private logs channel : {private_logs_channel}\n"
    #         message += f"⚙️ Maintenance status :</b> <code>{maintenance_status}</code>\n"
    #         message += f"<b>🔐 OTP website API key :</b> <code>{otp_api_key}</code>\n"
    #         message += f"<b>🧑‍🤝‍🧑 Per refer amount :</b> <code>{referral_range_display}</code>\n"
    #         message += f"<b>💸 Joining bonus amount :</b> <code>{bonus_range_display}</code>\n\n"
    #         message += f"<b>⚠️ Note :</b> <i>Send /admin again after setting all the things to see the changes.</i>"

    #         return message

    #     except Exception as e:
    #         logger.error(f"Error getting original admin message: {e}")
    #         return "<b>👋 Hello bro, welcome to the admin panel.\n\n⚠️ Note :</b> <i>Send /admin again after setting all the things to see the changes.</i>"

    # async def _get_original_admin_dashboard(self) -> InlineKeyboardMarkup:
    #     """Get original admin dashboard matching PHP version exactly"""
    #     keyboard = [
    #         [
    #             InlineKeyboardButton('➕ Add balance', callback_data='add'),
    #             InlineKeyboardButton('➖ Remove balance', callback_data='remove')
    #         ],
    #         [
    #             InlineKeyboardButton('🚫 Ban user', callback_data='ban'),
    #             InlineKeyboardButton('✔️ Unban user', callback_data='unban')
    #         ],
    #         [
    #             InlineKeyboardButton('🏘️ Set / Change main channel', callback_data='mainChannel')
    #         ],
    #         [
    #             InlineKeyboardButton('🤫 Set / Change private logs channel', callback_data='privateLogsChannel')
    #         ],
    #         [
    #             InlineKeyboardButton('➕ Add Force Sub Channel', callback_data='addForceSubChannel'),
    #             InlineKeyboardButton('➖ Remove Force Sub Channel', callback_data='removeForceSubChannel')
    #         ],
    #         [
    #             InlineKeyboardButton('📋 View Force Sub Channels', callback_data='viewForceSubChannels')
    #         ],
    #         [
    #             InlineKeyboardButton('⚙️ Set / Change maintenance status', callback_data='maintenanceStatus')
    #         ],
    #         [
    #             InlineKeyboardButton('🔐 Set / Change OTP website API key', callback_data='OTPWebsiteAPIKey')
    #         ],
    #         [
    #             InlineKeyboardButton('🧑‍🤝‍🧑 Set / Change per refer amount', callback_data='perReferAmount')
    #         ],
    #         [
    #             InlineKeyboardButton('💸 Set / Change joining bonus amount', callback_data='joiningBonusAmount')
    #         ],
    #         [
    #             InlineKeyboardButton('🏛️ Withdrawal Tax & Control', callback_data='withdrawal_settings')
    #         ],
    #         [
    #             InlineKeyboardButton('🔍 Check user\'s record', callback_data='checkUserRecord')
    #         ],
    #         [
    #             InlineKeyboardButton('🏧 Pass user\'s withdrawal', callback_data='passUserWithdrawal')
    #         ],
    #         [
    #             InlineKeyboardButton('🏧 Fail user\'s withdrawal', callback_data='failUserWithdrawal')
    #         ],
    #         [
    #             InlineKeyboardButton('🔄 Reset User Account Details', callback_data='resetUserAccount')
    #         ],
    #         [
    #             InlineKeyboardButton('🎁 Broadcast gift button', callback_data='broadcastGiftButton')
    #         ],
    #         [
    #             InlineKeyboardButton('📢 Broadcast Message', callback_data='broadcastText')
    #         ],
    #         [
    #             InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks'),
    #             InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')
    #         ],
    #         [
    #             InlineKeyboardButton('🎫 Generate Gift Code', callback_data='generateGiftCode')
    #         ],
    #         [
    #             InlineKeyboardButton('🏆 Configure Level Rewards', callback_data='configureLevelRewards'),
    #             InlineKeyboardButton('🔄 Toggle Level Bonus', callback_data='toggleLevelBonus')
    #         ],
    #         [
    #             InlineKeyboardButton('🔗 Custom Referral Links', callback_data='customReferralLinks')
    #         ]
    #     ]

    #     return InlineKeyboardMarkup(keyboard)

    # ==================== NEW ENHANCED ADMIN PANEL SYSTEM ====================

    async def _get_new_admin_message(self) -> str:
        """Get new enhanced admin panel message with exact specifications"""
        try:
            import time
            from telegram import Bot
            from config.settings import settings

            # Get bot statistics
            stats = await self.admin_service.get_bot_statistics()
            admin_settings = await self.admin_service.get_admin_settings()

            # Calculate bot ping
            start_time = time.time()
            try:
                bot = Bot(token=settings.BOT_TOKEN)
                await bot.get_me()
                ping_time = round((time.time() - start_time) * 1000, 2)
                bot_status = "🟢 Live"
            except Exception:
                ping_time = "N/A"
                bot_status = "🔴 Offline"

            # Get withdrawal settings
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})
            withdrawal_enabled = withdrawal_settings.get('enabled', True)
            withdrawal_status = "🟢 Enabled" if withdrawal_enabled else "🔴 Disabled"

            # Get maintenance status
            maintenance_status = admin_settings.get('maintenance_status', 'Off')
            maintenance_display = "Enabled" if maintenance_status == 'On' else "Disabled"

            # Get per refer and join bonus ranges
            per_refer_range = admin_settings.get('per_refer_amount_range', '20-50')
            join_bonus_range = admin_settings.get('joining_bonus_amount_range', '20-50')

            # Get total users and withdrawn amount
            total_users = stats.get('total_users', 0)
            total_withdrawn = stats.get('total_successful_withdrawals', 0)

            # Create the exact message format
            message = f"⚙️ Hello Admin, Welcome To Bot Settings.\n\n"
            message += f"▫️ Bot Status: {bot_status} (Ping: {ping_time}ms)\n"
            message += f"▫️ Withdrawal Status: {withdrawal_status}\n"
            message += f"▫️ Maintenance Status: {maintenance_display} (toggleable)\n"
            message += f"▫️ Per Refer: ₹{per_refer_range} (from settings)\n"
            message += f"▫️ Join Bonus: ₹{join_bonus_range} (from settings)\n"
            message += f"▫️ Total Users: {total_users}\n"
            message += f"▫️ Withdrawn: ₹{total_withdrawn}"

            return message

        except Exception as e:
            logger.error(f"Error getting new admin message: {e}")
            return "⚙️ Hello Admin, Welcome To Bot Settings.\n\n❌ Error loading bot statistics."

    async def _get_new_admin_dashboard(self) -> InlineKeyboardMarkup:
        """Get new enhanced admin dashboard with improved horizontal/vertical layout"""
        try:
            keyboard = [
                # Financial Management - Horizontal pair
                [
                    InlineKeyboardButton('💰 Manage Withdrawals', callback_data='manage_withdrawals'),
                    InlineKeyboardButton('💰 User Bonus', callback_data='user_bonus')
                ],
                # User Management - Single wide button
                [
                    InlineKeyboardButton('👤 User Details & Settings', callback_data='user_details_settings')
                ],
                # Content Management - Horizontal pair
                [
                    InlineKeyboardButton('📋 Manage Tasks', callback_data='manage_tasks'),
                    InlineKeyboardButton('👨‍💼 Manage Admins', callback_data='manage_admins')
                ],
                # Communication - Horizontal pair
                [
                    InlineKeyboardButton('📬 User Broadcast', callback_data='user_broadcast'),
                    InlineKeyboardButton('🎁 Gift Broadcast', callback_data='gift_broadcast')
                ],
                # Analytics - Single wide button
                [
                    InlineKeyboardButton('📊 Bot Statistics & Analysis', callback_data='bot_statistics')
                ],
                # Rewards & Channels - Horizontal pair
                [
                    InlineKeyboardButton('🎁 Manage Redeem Codes', callback_data='manage_gift_codes'),
                    InlineKeyboardButton('🌟 ForceSub Channels', callback_data='forcesub_channels')
                ],
                # System Settings - Horizontal pair
                [
                    InlineKeyboardButton('⚠️ Bot Maintenance', callback_data='bot_maintenance'),
                    InlineKeyboardButton('🔗 Custom Referral Link', callback_data='custom_referral_link')
                ],
                # Database Management - Single wide button
                [
                    InlineKeyboardButton('🗑️ Database Cleanup', callback_data='database_cleanup')
                ]
            ]

            return InlineKeyboardMarkup(keyboard)

        except Exception as e:
            logger.error(f"Error getting new admin dashboard: {e}")
            # Fallback keyboard with improved layout
            return InlineKeyboardMarkup([
                # Financial Management - Horizontal pair
                [
                    InlineKeyboardButton('💰 Manage Withdrawals', callback_data='manage_withdrawals'),
                    InlineKeyboardButton('💰 User Bonus', callback_data='user_bonus')
                ],
                # User Management - Single wide button
                [InlineKeyboardButton('👤 User Details & Settings', callback_data='user_details_settings')],
                # Content Management - Horizontal pair
                [
                    InlineKeyboardButton('📋 Manage Tasks', callback_data='manage_tasks'),
                    InlineKeyboardButton('👨‍💼 Manage Admins', callback_data='manage_admins')
                ],
                # Communication - Horizontal pair
                [
                    InlineKeyboardButton('📬 User Broadcast', callback_data='user_broadcast'),
                    InlineKeyboardButton('🎁 Gift Broadcast', callback_data='gift_broadcast')
                ],
                # Analytics - Single wide button
                [InlineKeyboardButton('📊 Bot Statistics & Analysis', callback_data='bot_statistics')],
                # Rewards & Channels - Horizontal pair
                [
                    InlineKeyboardButton('🎁 Manage Redeem Codes', callback_data='manage_gift_codes'),
                    InlineKeyboardButton('🌟 ForceSub Channels', callback_data='forcesub_channels')
                ],
                # System Settings - Horizontal pair
                [
                    InlineKeyboardButton('⚠️ Bot Maintenance', callback_data='bot_maintenance'),
                    InlineKeyboardButton('🔗 Custom Referral Link', callback_data='custom_referral_link')
                ]
            ])

    # ==================== NEW ADMIN PANEL HANDLERS ====================

    # ==================== TASK MANAGEMENT PANEL ====================

    async def handle_manage_tasks(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Task Management Panel"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get task statistics
            from services.task_service import TaskService
            task_service = TaskService()

            # Get all tasks and statistics
            all_tasks = await task_service.get_all_tasks()
            active_tasks = [task for task in all_tasks if task.get('status') == 'active']
            task_stats = await task_service.get_task_statistics()

            # Build message
            message = "⚙️ Hello, Welcome To The Task Settings.\n\n"
            message += f"🏷 Total Live Tasks: {len(active_tasks)}\n\n"
            message += f"▫️ Screenshots Submitted: {task_stats.get('total_submissions', 0)}\n"
            message += f"▫️ Total Screenshots Rejected: {task_stats.get('rejected_submissions', 0)}\n"
            message += f"▫️ Total Screenshots Approved: {task_stats.get('approved_submissions', 0)}\n"
            message += f"▫️ Total Pending Screenshots: {task_stats.get('pending_submissions', 0)}"

            # Build keyboard with dynamic task buttons
            keyboard_buttons = []

            # Add dynamic task buttons (only for existing tasks)
            if all_tasks:
                for task in all_tasks:
                    task_name = task['name']
                    # Truncate long task names for button display
                    display_name = task_name if len(task_name) <= 30 else task_name[:27] + "..."
                    keyboard_buttons.append([
                        InlineKeyboardButton(display_name, callback_data=f"manage_task_{task['task_id']}")
                    ])

            # Add static action buttons
            keyboard_buttons.extend([
                [InlineKeyboardButton('➕ Add Task', callback_data='add_task')],
                [InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')]
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            await self._safe_edit_message(query, message, keyboard, context)

        except Exception as e:
            logger.error(f"Error in handle_manage_tasks: {e}")
            error_message = "❌ <b>Error</b>\n\nSomething went wrong while loading task management. Please try again later."
            await self._safe_edit_message(query, error_message, None, context)

    async def handle_manage_individual_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle Individual Task Management Panel"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get task details
            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nTask not found.",
                    parse_mode='HTML'
                )
                return

            # Get task submission statistics
            pending_count = await task_service.get_pending_submissions_count(task_id)
            approved_count = await task_service.get_approved_submissions_count(task_id)
            rejected_count = await task_service.get_rejected_submissions_count(task_id)
            total_submissions = pending_count + approved_count + rejected_count

            # Build message
            message = "⚙️ Hello, Manage Task From Here.\n\n"
            message += f"▫️ Name: {task['name']}\n"
            message += f"▫️ Bonus Amount: ₹{task['reward_amount']}\n"
            message += f"▫️ Task Media URL: {task.get('media_url', 'Not set')}\n"
            message += f"▫️ Pending Screenshots: {pending_count}\n"
            message += f"📥 Screenshots Uploaded: {total_submissions} (Approved: {approved_count} | Rejected: {rejected_count})"

            # Build keyboard with task edit buttons
            keyboard = [
                [
                    InlineKeyboardButton('✏️ Edit Task Name', callback_data=f'edit_task_name_{task_id}')
                ],
                [
                    InlineKeyboardButton('💰 Edit Task Bonus', callback_data=f'edit_task_bonus_{task_id}')
                ],
                [
                    InlineKeyboardButton('📝 Edit Task Content', callback_data=f'edit_task_content_{task_id}')
                ],
                [
                    InlineKeyboardButton('🖼️ Edit Task Image URL', callback_data=f'edit_task_image_{task_id}')
                ],
                [
                    InlineKeyboardButton('⚠️ Delete Task', callback_data=f'delete_task_{task_id}')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_tasks')
                ]
            ]

            await self._safe_edit_message(query, message, InlineKeyboardMarkup(keyboard), context)

        except Exception as e:
            logger.error(f"Error in handle_manage_individual_task: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading task details. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_edit_task_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task name"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "✏️ <b>Edit Task Name</b>\n\n"
            message += "Send the new task name to set:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Details', callback_data=f'manage_task_{task_id}')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_name', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_name: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task bonus"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "💰 <b>Edit Task Bonus</b>\n\n"
            message += "Send the new task bonus amount to set:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Details', callback_data=f'manage_task_{task_id}')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_bonus', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_content(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task content"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📝 <b>Edit Task Content</b>\n\n"
            message += "Send the new task content/steps to set:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Details', callback_data=f'manage_task_{task_id}')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_content', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_content: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_image(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle edit task image URL"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🖼️ <b>Edit Task Image URL</b>\n\n"
            message += "Send the new image URL to set:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Details', callback_data=f'manage_task_{task_id}')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_image', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_image: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str) -> None:
        """Handle delete task"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Delete the task
            from services.task_service import TaskService
            task_service = TaskService()

            success = await task_service.delete_task(task_id)

            if success:
                await query.answer("✅ Task Has Been Successfully Deleted..✅️", show_alert=True)

                # Show go back button
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Go Back', callback_data='manage_tasks')]
                ])

                await query.edit_message_text(
                    "✅ <b>Task Deleted Successfully</b>\n\nThe task has been removed from the system.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await query.answer("❌ Failed to delete task.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_delete_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_add_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle add new task workflow"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add New Task</b>\n\n"
            message += "Enter the Name and Bonus, separated by (-)\n\n"
            message += "e.g., Promote & Earn-5\n\n"
            message += "- Task Name: Promote & Earn\n"
            message += "- Bonus: ₹5\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Management', callback_data='manage_tasks')]
            ])

            await self._safe_edit_message(query, message, keyboard, context)

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_task_step1')

        except Exception as e:
            logger.error(f"Error in handle_add_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== ADMIN MANAGEMENT SYSTEM ====================

    async def handle_manage_admins(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Admin Management Panel (6.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current admin list
            admin_settings = await self.admin_service.get_admin_settings()
            admin_list = admin_settings.get('admin_list', [])

            # Build message
            message = "⚙️ Hello, Welcome To Admin Settings.\n\n"
            message += f"👨🏻‍💻 Total Admins Added: {len(admin_list)}\n\n"

            # Display numbered list of admin IDs
            if admin_list:
                for i, admin_id in enumerate(admin_list, 1):
                    message += f"{i}. {admin_id}\n"
            else:
                message += "No additional admins configured."

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton('➕ Add Admin', callback_data='add_admin')
                ],
                [
                    InlineKeyboardButton('➖ Remove Admin', callback_data='remove_admin')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_admins: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading admin management. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_add_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Add Admin workflow (6.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add Admin</b>\n\n"
            message += "Send user's chat ID to promote as admin:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Management', callback_data='manage_admins')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_admin')

        except Exception as e:
            logger.error(f"Error in handle_add_admin: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_admin(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Remove Admin workflow (6.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➖ <b>Remove Admin</b>\n\n"
            message += "Send user's chat ID to remove admin rights:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Management', callback_data='manage_admins')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_admin')

        except Exception as e:
            logger.error(f"Error in handle_remove_admin: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== USER BROADCAST SYSTEM ====================

    async def handle_user_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle User Broadcast Panel (7.0) with Content Status and Reset Options"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current broadcast draft to show content status
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            draft = await broadcast_service.get_broadcast_draft(user_id)

            # Build content status message
            message = "📬 <b>Broadcast Panel</b>\n"
            message += "Send a message to all bot users simultaneously.\n\n"

            # Content status indicators
            content_status = []
            has_content = False

            if draft:
                if draft.get('media'):
                    media_type = draft['media'].get('type', 'Unknown').title()
                    content_status.append(f"🖼️ <b>Media:</b> {media_type} ✅")
                    has_content = True
                else:
                    content_status.append("🖼️ <b>Media:</b> Not set")

                if draft.get('text'):
                    text_preview = draft['text'][:30] + "..." if len(draft['text']) > 30 else draft['text']
                    content_status.append(f"🔤 <b>Text:</b> {text_preview} ✅")
                    has_content = True
                else:
                    content_status.append("🔤 <b>Text:</b> Not set")

                if draft.get('buttons'):
                    # Count total buttons
                    button_count = 0
                    try:
                        for button_row in draft['buttons']:
                            if isinstance(button_row, list):
                                button_count += len(button_row)
                            elif isinstance(button_row, dict):
                                button_count += 1
                    except:
                        button_count = len(draft['buttons'])

                    content_status.append(f"⌨️ <b>Buttons:</b> {button_count} configured ✅")
                    has_content = True
                else:
                    content_status.append("⌨️ <b>Buttons:</b> Not set")
            else:
                content_status = [
                    "🖼️ <b>Media:</b> Not set",
                    "🔤 <b>Text:</b> Not set",
                    "⌨️ <b>Buttons:</b> Not set"
                ]

            message += "\n".join(content_status)

            if has_content:
                message += "\n\n💡 <i>Use reset options to clear specific content</i>"

            # Build keyboard with content setup and reset options
            keyboard = []

            # Content Setup Row
            keyboard.append([
                InlineKeyboardButton('🖼️ Media', callback_data='broadcast_media'),
                InlineKeyboardButton('🔤 Text', callback_data='broadcast_text'),
                InlineKeyboardButton('⌨️ Buttons', callback_data='broadcast_buttons')
            ])

            # Reset Options Row (only show if there's content to reset)
            if has_content:
                reset_row = []
                if draft.get('media'):
                    reset_row.append(InlineKeyboardButton('🗑️ Reset Media', callback_data='reset_broadcast_media'))
                if draft.get('text'):
                    reset_row.append(InlineKeyboardButton('🗑️ Reset Text', callback_data='reset_broadcast_text'))
                if draft.get('buttons'):
                    reset_row.append(InlineKeyboardButton('🗑️ Reset Buttons', callback_data='reset_broadcast_buttons'))

                # Split reset buttons into rows if too many
                if len(reset_row) > 2:
                    keyboard.append(reset_row[:2])
                    if len(reset_row) > 2:
                        keyboard.append(reset_row[2:])
                elif reset_row:
                    keyboard.append(reset_row)

                # Clear All button
                keyboard.append([
                    InlineKeyboardButton('🧹 Clear All Content', callback_data='reset_broadcast_all')
                ])

            # Preview & Action Row
            preview_row = []
            if has_content:
                preview_row.append(InlineKeyboardButton('👀 Full Preview', callback_data='broadcast_preview'))
                preview_row.append(InlineKeyboardButton('▶️ Start Broadcast', callback_data='start_broadcast'))
            else:
                preview_row.append(InlineKeyboardButton('👀 Preview (Empty)', callback_data='broadcast_preview'))

            keyboard.append(preview_row)

            # Navigation Row
            keyboard.append([
                InlineKeyboardButton('🔙 Back to Admin Panel', callback_data='admin_panel')
            ])

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading broadcast panel. Please try again later.",
                parse_mode='HTML'
            )







    async def handle_broadcast_preview(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle comprehensive broadcast preview"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get broadcast data from session or storage
            from services.broadcast_service import BroadcastService
            from services.user_service import UserService
            broadcast_service = BroadcastService()
            user_service = UserService()

            broadcast_data = await broadcast_service.get_broadcast_draft(user_id)

            if not broadcast_data:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await query.edit_message_text(
                    "❌ <b>No Broadcast Data</b>\n\nPlease configure media, text, or buttons first.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Get recipient count
            all_users = await user_service.get_all_users()
            active_users = [user for user in all_users if not user.get('banned', False)]
            recipient_count = len(active_users)

            # Build preview message
            preview_message = "📋 <b>Broadcast Preview</b>\n\n"
            preview_message += "👥 <b>Recipients:</b> {} active users\n\n".format(recipient_count)

            # Content summary with defensive programming
            content_parts = []
            try:
                if broadcast_data.get('media') and isinstance(broadcast_data['media'], dict):
                    media_type = broadcast_data['media'].get('type', 'Unknown').title()
                    content_parts.append(f"🖼️ {media_type}")
                    if broadcast_data['media'].get('caption'):
                        content_parts.append(f"📝 Caption")
            except Exception as e:
                logger.warning(f"Error processing media data in preview: {e}")
                content_parts.append("🖼️ Media")

            if broadcast_data.get('text'):
                content_parts.append("🔤 Text Message")

            if broadcast_data.get('buttons'):
                # Count total buttons across all rows
                button_count = 0
                try:
                    for button_row in broadcast_data['buttons']:
                        if isinstance(button_row, list):
                            button_count += len(button_row)
                        elif isinstance(button_row, dict):
                            button_count += 1  # Legacy single button
                except Exception as e:
                    logger.warning(f"Error counting buttons: {e}")
                    button_count = len(broadcast_data['buttons'])  # Fallback

                content_parts.append(f"⌨️ {button_count} Button(s)")

            if content_parts:
                preview_message += f"📦 <b>Content:</b> {', '.join(content_parts)}\n\n"

            # Show actual content preview
            preview_message += "👀 <b>User Will See:</b>\n"
            preview_message += "━━━━━━━━━━━━━━━━━━━━\n"

            # Text content with safe handling
            try:
                if broadcast_data.get('text') and isinstance(broadcast_data['text'], str):
                    text_preview = broadcast_data['text'][:200] + "..." if len(broadcast_data['text']) > 200 else broadcast_data['text']
                    preview_message += f"{text_preview}\n"
            except Exception as e:
                logger.warning(f"Error processing text preview: {e}")
                preview_message += "Text content configured\n"

            # Media caption with safe handling
            try:
                if (broadcast_data.get('media') and
                    isinstance(broadcast_data['media'], dict) and
                    broadcast_data['media'].get('caption') and
                    isinstance(broadcast_data['media']['caption'], str)):
                    caption_preview = broadcast_data['media']['caption'][:100] + "..." if len(broadcast_data['media']['caption']) > 100 else broadcast_data['media']['caption']
                    preview_message += f"\n📷 <i>{caption_preview}</i>\n"
            except Exception as e:
                logger.warning(f"Error processing media caption preview: {e}")

            # Buttons preview
            if broadcast_data.get('buttons'):
                preview_message += "\n⌨️ <b>Buttons:</b>\n"
                try:
                    # Handle the correct button data structure: list of lists of dicts
                    for button_row in broadcast_data['buttons']:
                        if isinstance(button_row, list):
                            # New structure: list of button rows
                            for button in button_row:
                                if isinstance(button, dict) and 'text' in button:
                                    preview_message += f"🔘 {button['text']}\n"
                        elif isinstance(button_row, dict) and 'text' in button_row:
                            # Legacy structure: direct button dict (fallback)
                            preview_message += f"🔘 {button_row['text']}\n"
                except Exception as e:
                    logger.warning(f"Error processing button preview: {e}")
                    preview_message += "🔘 Buttons configured\n"

            preview_message += "━━━━━━━━━━━━━━━━━━━━\n\n"
            preview_message += "⚡ Ready to broadcast to all users?"

            # Build keyboard with preview options
            # Build keyboard with reset options
            keyboard_rows = []

            # Start and Edit row
            keyboard_rows.append([
                InlineKeyboardButton('✅ Start Broadcast', callback_data='confirm_start_broadcast'),
                InlineKeyboardButton('✏️ Edit Content', callback_data='userBroadcast')
            ])

            # Reset options row (if content exists)
            reset_row = []
            if broadcast_data.get('text'):
                reset_row.append(InlineKeyboardButton('🗑️ Reset Text', callback_data='reset_broadcast_text'))
            if broadcast_data.get('media'):
                reset_row.append(InlineKeyboardButton('🗑️ Reset Media', callback_data='reset_broadcast_media'))
            if broadcast_data.get('buttons'):
                reset_row.append(InlineKeyboardButton('🗑️ Reset Buttons', callback_data='reset_broadcast_buttons'))

            # Split reset buttons into rows if too many
            if len(reset_row) > 2:
                keyboard_rows.append(reset_row[:2])
                if len(reset_row) > 2:
                    keyboard_rows.append(reset_row[2:])
            elif reset_row:
                keyboard_rows.append(reset_row)

            # Clear all row (if any content exists)
            if broadcast_data.get('text') or broadcast_data.get('media') or broadcast_data.get('buttons'):
                keyboard_rows.append([
                    InlineKeyboardButton('🧹 Clear All Content', callback_data='reset_broadcast_all')
                ])

            # Back row
            keyboard_rows.append([
                InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')
            ])

            keyboard = InlineKeyboardMarkup(keyboard_rows)

            # Send media preview if available
            if broadcast_data.get('media') and isinstance(broadcast_data['media'], dict):
                try:
                    media = broadcast_data['media']
                    media_type = media.get('type')
                    file_id = media.get('file_id')

                    if not media_type or not file_id:
                        logger.warning("Media data missing type or file_id")
                        preview_message += "\n⚠️ <i>Media preview unavailable (incomplete data)</i>\n"
                    elif media_type == 'photo':
                        await context.bot.send_photo(
                            chat_id=query.message.chat_id,
                            photo=file_id,
                            caption="📸 <b>Media Preview</b> (This will be sent to users)",
                            parse_mode='HTML'
                        )
                    elif media_type == 'video':
                        await context.bot.send_video(
                            chat_id=query.message.chat_id,
                            video=file_id,
                            caption="🎥 <b>Media Preview</b> (This will be sent to users)",
                            parse_mode='HTML'
                        )
                    elif media_type == 'document':
                        await context.bot.send_document(
                            chat_id=query.message.chat_id,
                            document=file_id,
                            caption="📄 <b>Media Preview</b> (This will be sent to users)",
                            parse_mode='HTML'
                        )
                    else:
                        logger.info(f"Media type '{media_type}' not supported for preview")
                        preview_message += f"\n📎 <i>{media_type.title()} media configured</i>\n"

                except Exception as e:
                    logger.warning(f"Could not preview media: {e}")
                    preview_message += "\n⚠️ <i>Media preview unavailable</i>\n"

            await query.edit_message_text(
                text=preview_message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_broadcast_preview: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while generating preview. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_confirm_start_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle confirmed broadcast start"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get broadcast data
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            broadcast_data = await broadcast_service.get_broadcast_draft(user_id)

            if not broadcast_data or not (broadcast_data.get('text') or broadcast_data.get('media')):
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await query.edit_message_text(
                    "❌ <b>No Broadcast Content</b>\n\nPlease configure text or media before starting broadcast.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Initialize broadcast
            broadcast_id = await broadcast_service.start_broadcast(user_id, broadcast_data)

            if not broadcast_id:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to start broadcast.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Clear broadcast draft
            await broadcast_service.clear_broadcast_draft(user_id)

            # Show success message
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin_panel')]
            ])

            await query.edit_message_text(
                f"✅ <b>Broadcast Started</b>\n\n"
                f"🆔 <b>Broadcast ID:</b> {broadcast_id[-8:]}\n"
                f"📡 Your message is being sent to all users.\n\n"
                f"You can monitor progress in the admin panel.",
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_confirm_start_broadcast: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_start_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Start Broadcast (7.5)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get broadcast data
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            broadcast_data = await broadcast_service.get_broadcast_draft(user_id)

            if not broadcast_data or not (broadcast_data.get('text') or broadcast_data.get('media')):
                await query.edit_message_text(
                    "❌ <b>No Broadcast Content</b>\n\nPlease configure text or media before starting broadcast.",
                    parse_mode='HTML'
                )
                return

            # Show confirmation with preview
            message = "📬 Broadcast\n\n"
            message += "Are you really sure you want to do this?\n\n"
            message += "<b>Preview:</b>\n"

            if broadcast_data.get('text'):
                preview_text = broadcast_data['text'][:100] + "..." if len(broadcast_data['text']) > 100 else broadcast_data['text']
                message += f"Text: {preview_text}\n"

            if broadcast_data.get('media'):
                message += f"Media: {broadcast_data['media']['type']}\n"

            if broadcast_data.get('buttons'):
                # Count total buttons across all rows
                button_count = 0
                try:
                    for button_row in broadcast_data['buttons']:
                        if isinstance(button_row, list):
                            button_count += len(button_row)
                        elif isinstance(button_row, dict):
                            button_count += 1  # Legacy single button
                except Exception as e:
                    logger.warning(f"Error counting buttons in start broadcast: {e}")
                    button_count = len(broadcast_data['buttons'])  # Fallback to row count

                message += f"Buttons: {button_count} configured\n"

            keyboard = [
                [
                    InlineKeyboardButton('❌ NO', callback_data='user_broadcast'),
                    InlineKeyboardButton('✅ YES', callback_data='confirm_broadcast')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_start_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while preparing broadcast. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_confirm_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Broadcast Execution Confirmation"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Start broadcast execution
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Get broadcast data
            broadcast_data = await broadcast_service.get_broadcast_draft(user_id)
            if not broadcast_data:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nBroadcast data not found.",
                    parse_mode='HTML'
                )
                return

            # Initialize broadcast
            broadcast_id = await broadcast_service.start_broadcast(user_id, broadcast_data)

            if not broadcast_id:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to start broadcast.",
                    parse_mode='HTML'
                )
                return

            # Show initial progress message
            progress_message = "⏳ Sleeping for 1 seconds\n\n"
            progress_message += "✅ Broadcasted To: 0\n"
            progress_message += "🗨 Users Left: Calculating..."

            await query.edit_message_text(
                text=progress_message,
                parse_mode='HTML'
            )

            # Execute optimized broadcast in background
            async def progress_callback(stats, processed_count, total_users, start_time):
                try:
                    # Calculate progress percentage
                    progress_percent = round((processed_count / total_users) * 100, 1) if total_users > 0 else 0

                    # Calculate ETA
                    elapsed_time = get_current_timestamp() - start_time
                    if processed_count > 0:
                        avg_time_per_user = elapsed_time / processed_count
                        remaining_users = total_users - processed_count
                        eta_seconds = avg_time_per_user * remaining_users
                        eta_minutes = int(eta_seconds / 60)
                        eta_text = f"{eta_minutes}m {int(eta_seconds % 60)}s" if eta_minutes > 0 else f"{int(eta_seconds)}s"
                    else:
                        eta_text = "Calculating..."

                    # Update progress message
                    progress_message = f"📊 <b>Broadcast Progress</b>\n\n"
                    progress_message += f"⏳ <b>Progress:</b> {processed_count:,}/{total_users:,} ({progress_percent}%)\n"
                    progress_message += f"✅ <b>Sent:</b> {stats['successful_sends']:,}\n"
                    progress_message += f"❌ <b>Failed:</b> {stats['failed_sends']:,}\n"
                    progress_message += f"🚫 <b>Blocked:</b> {stats['blocked_users']:,}\n"
                    progress_message += f"💀 <b>Deactivated:</b> {stats['deactivated_users']:,}\n"
                    progress_message += f"🔍 <b>Not Found:</b> {stats['chat_not_found']:,}\n"
                    progress_message += f"⚡ <b>Rate Limited:</b> {stats['rate_limited']:,}\n"
                    progress_message += f"🧹 <b>Cleaned:</b> {len(stats['cleaned_users']):,}\n\n"
                    progress_message += f"⏱️ <b>ETA:</b> {eta_text}"

                    await query.edit_message_text(progress_message, parse_mode='HTML')
                except Exception as e:
                    logger.error(f"Error updating progress: {e}")

            # Execute optimized broadcast
            result = await broadcast_service.execute_broadcast_optimized(
                broadcast_id, user_id, progress_callback
            )

            if result["success"]:
                stats = result["statistics"]
                success_rate = round((stats["successful_sends"] / stats["total_users"]) * 100, 2) if stats["total_users"] > 0 else 0

                completion_message = f"✅ <b>Broadcast Completed!</b>\n\n"
                completion_message += f"🆔 <b>ID:</b> {broadcast_id[-8:]}\n"
                completion_message += f"👥 <b>Total:</b> {stats['total_users']:,}\n"
                completion_message += f"✅ <b>Sent:</b> {stats['successful_sends']:,}\n"
                completion_message += f"❌ <b>Failed:</b> {stats['failed_sends']:,}\n"
                completion_message += f"🚫 <b>Blocked:</b> {stats['blocked_users']:,}\n"
                completion_message += f"💀 <b>Deactivated:</b> {stats['deactivated_users']:,}\n"
                completion_message += f"🧹 <b>Cleaned:</b> {len(stats['cleaned_users']):,}\n"
                completion_message += f"📈 <b>Success Rate:</b> {success_rate}%"

                await query.edit_message_text(completion_message, parse_mode='HTML')
            else:
                await query.edit_message_text(f"❌ Broadcast failed: {result['error']}")

        except Exception as e:
            logger.error(f"Error in handle_confirm_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while starting broadcast. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== BROADCAST RESET SYSTEM ====================

    async def handle_reset_broadcast_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle reset broadcast text"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}

            if not draft.get('text'):
                await query.edit_message_text(
                    "ℹ️ <b>No Text to Reset</b>\n\nThere is no text content configured in your broadcast draft.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
                return

            # Remove text from draft
            if 'text' in draft:
                del draft['text']

            # Save updated draft
            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            if success:
                await query.edit_message_text(
                    "✅ <b>Text Content Cleared</b>\n\nThe text content has been successfully removed from your broadcast draft.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
            else:
                await query.edit_message_text(
                    "❌ <b>Reset Failed</b>\n\nFailed to clear text content. Please try again.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_reset_broadcast_text: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while resetting text. Please try again later.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                ]),
                parse_mode='HTML'
            )

    async def handle_reset_broadcast_media(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle reset broadcast media"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}

            if not draft.get('media'):
                await query.edit_message_text(
                    "ℹ️ <b>No Media to Reset</b>\n\nThere is no media content configured in your broadcast draft.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
                return

            # Remove media from draft
            if 'media' in draft:
                del draft['media']

            # Save updated draft
            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            if success:
                await query.edit_message_text(
                    "✅ <b>Media Content Cleared</b>\n\nThe media content has been successfully removed from your broadcast draft.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
            else:
                await query.edit_message_text(
                    "❌ <b>Reset Failed</b>\n\nFailed to clear media content. Please try again.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_reset_broadcast_media: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while resetting media. Please try again later.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                ]),
                parse_mode='HTML'
            )

    async def handle_reset_broadcast_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle reset broadcast buttons"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}

            if not draft.get('buttons'):
                await query.edit_message_text(
                    "ℹ️ <b>No Buttons to Reset</b>\n\nThere are no buttons configured in your broadcast draft.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
                return

            # Remove buttons from draft
            if 'buttons' in draft:
                del draft['buttons']

            # Save updated draft
            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            if success:
                await query.edit_message_text(
                    "✅ <b>Button Content Cleared</b>\n\nAll buttons have been successfully removed from your broadcast draft.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
            else:
                await query.edit_message_text(
                    "❌ <b>Reset Failed</b>\n\nFailed to clear button content. Please try again.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_reset_broadcast_buttons: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while resetting buttons. Please try again later.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                ]),
                parse_mode='HTML'
            )

    async def handle_reset_broadcast_all(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle reset all broadcast content with confirmation"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}

            # Check if there's any content to clear
            has_content = bool(draft.get('text') or draft.get('media') or draft.get('buttons'))

            if not has_content:
                await query.edit_message_text(
                    "ℹ️ <b>No Content to Clear</b>\n\nYour broadcast draft is already empty. There's nothing to reset.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
                return

            # Show confirmation with content summary
            message = "⚠️ <b>Clear All Content</b>\n\n"
            message += "Are you sure you want to clear ALL broadcast content?\n\n"
            message += "<b>This will remove:</b>\n"

            content_items = []
            if draft.get('text'):
                text_preview = draft['text'][:50] + "..." if len(draft['text']) > 50 else draft['text']
                content_items.append(f"🔤 Text: {text_preview}")

            if draft.get('media'):
                media_type = draft['media'].get('type', 'Unknown').title()
                content_items.append(f"🖼️ Media: {media_type}")

            if draft.get('buttons'):
                # Count total buttons
                button_count = 0
                try:
                    for button_row in draft['buttons']:
                        if isinstance(button_row, list):
                            button_count += len(button_row)
                        elif isinstance(button_row, dict):
                            button_count += 1
                except:
                    button_count = len(draft['buttons'])
                content_items.append(f"⌨️ Buttons: {button_count} configured")

            message += "\n".join(content_items)
            message += "\n\n<b>⚠️ This action cannot be undone!</b>"

            keyboard = [
                [
                    InlineKeyboardButton('✅ Yes, Clear All', callback_data='confirm_reset_all'),
                    InlineKeyboardButton('❌ Cancel', callback_data='user_broadcast')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_reset_broadcast_all: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while preparing to clear content. Please try again later.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                ]),
                parse_mode='HTML'
            )

    async def handle_confirm_reset_all(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle confirmed reset of all broadcast content"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Clear all broadcast content by creating empty draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Save empty draft (this effectively clears everything)
            success = await broadcast_service.save_broadcast_draft(user_id, {})

            if success:
                await query.edit_message_text(
                    "✅ <b>All Content Cleared</b>\n\n"
                    "Your broadcast draft has been completely reset. You can now start creating a new broadcast from scratch.\n\n"
                    "🆕 <i>Ready to create a fresh broadcast!</i>",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('🔄 Back to Broadcast Panel', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )
            else:
                await query.edit_message_text(
                    "❌ <b>Clear Failed</b>\n\nFailed to clear all content. Please try again.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                    ]),
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_confirm_reset_all: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while clearing content. Please try again later.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                ]),
                parse_mode='HTML'
            )

    # ==================== BOT STATISTICS & ANALYSIS SYSTEM ====================

    async def handle_bot_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Bot Statistics & Analysis Panel (8.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📊 View Bot's Live Stats & Analysis.\n\n"
            message += "▫️ Get overall stats for the bot's performance.\n"
            message += "▫️ Get top 30 users based on balance/invites.\n"
            message += "▫️ Get separate join stats for each channel."

            # Build keyboard with improved layout
            keyboard = [
                # Overall Stats - Single wide button
                [
                    InlineKeyboardButton('📊 Overall Bot Statistics', callback_data='overall_statistics')
                ],
                # Top Rankings - Horizontal trio
                [
                    InlineKeyboardButton('💰 Top Balances', callback_data='top_balances'),
                    InlineKeyboardButton('👥 Top Inviters', callback_data='top_inviters'),
                    InlineKeyboardButton('🏆 Top Withdrawers', callback_data='top_withdrawers')
                ],
                # Analytics - Horizontal pair
                [
                    InlineKeyboardButton('📈 Channel Wise Stats', callback_data='channel_stats'),
                    InlineKeyboardButton('⏳ Pending Withdrawals', callback_data='pending_withdrawals')
                ],
                # User History - Single button
                [
                    InlineKeyboardButton('👥 History Of Users', callback_data='user_history')
                ],
                # Navigation - Single button
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_bot_statistics: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading statistics panel. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_overall_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Overall Bot Statistics (8.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get statistics
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            stats = await stats_service.get_overall_statistics()

            # Statistics service now always returns a dict with fallback values
            # No need to check for empty dict since we provide defaults

            # Build comprehensive message
            message = "📊 <b>Overall Bot Statistics</b>\n\n"

            # User Statistics
            message += "👥 <b>User Statistics:</b>\n"
            message += f"▫️ Total Users: {stats.get('total_users', 0):,}\n"
            message += f"▫️ Active Users: {stats.get('active_users', 0):,}\n"
            message += f"▫️ Users with Referrals: {stats.get('users_with_referrals', 0):,}\n\n"

            # Financial Statistics
            message += "💰 <b>Financial Statistics:</b>\n"
            message += f"▫️ Total User Balance: ₹{stats.get('total_user_balance', 0):,.2f}\n"
            message += f"▫️ Total Referral Earnings: ₹{stats.get('total_referral_earnings', 0):,.2f}\n"
            message += f"▫️ Total Referrals Made: {stats.get('total_referrals', 0):,}\n\n"

            # Withdrawal Statistics
            message += "💸 <b>Withdrawal Statistics:</b>\n"
            message += f"▫️ Completed Withdrawals: {stats.get('completed_withdrawals', 0):,}\n"
            message += f"   🏦 Bank: {stats.get('bank_passed_withdrawals', 0):,} | ₿ USDT: {stats.get('usdt_passed_withdrawals', 0):,}\n"
            message += f"▫️ Total Withdrawn: ₹{stats.get('total_withdrawn', 0):,.2f}\n"
            message += f"   🏦 Bank: ₹{stats.get('bank_passed_amount', 0):,.2f} | ₿ USDT: ₹{stats.get('usdt_passed_amount', 0):,.2f}\n"
            message += f"▫️ Pending Withdrawals: {stats.get('pending_withdrawals', 0):,} (₹{stats.get('pending_withdrawal_amount', 0):,.2f})\n\n"

            # Account Information
            message += "🏦 <b>Account Information:</b>\n"
            message += f"▫️ Unique Bank Accounts: {stats.get('unique_banks_count', 0):,}\n"
            message += f"▫️ Unique USDT Addresses: {stats.get('unique_usdt_count', 0):,}\n\n"

            # Rewards & Activity
            message += "🎁 <b>Rewards & Activity:</b>\n"
            message += f"▫️ Gift Codes Claimed: {stats.get('claimed_codes_count', 0):,}\n"
            message += f"▫️ Gift Code Value: ₹{stats.get('total_gift_value', 0):,.2f}\n"
            message += f"▫️ Task Submissions: {stats.get('total_task_submissions', 0):,}\n\n"

            # System Performance
            message += "⚡ <b>System Performance:</b>\n"
            message += f"▫️ Database Status: ✅ Connected\n"
            message += f"▫️ Statistics Cache: ✅ Active\n"
            message += f"▫️ Last Updated: Just now"
            message += f"▫️ Total Screenshots Submitted: {stats.get('total_task_submissions', 0)}"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='overall_statistics')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_overall_statistics: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading overall statistics. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_top_balances(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Top 30 Balances (8.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get top balances
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            top_users = await stats_service.get_top_balances(30)

            if not top_users:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo users with balance found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "💰 Top 30 Balances:\n\n"

            for i, user in enumerate(top_users, 1):
                username = user.get('username', 'N/A')
                first_name = user.get('first_name', 'Unknown')
                balance = user.get('balance', 0)
                user_id_str = str(user.get('user_id', 'N/A'))

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Balance: ₹{balance:.2f}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='top_balances')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_top_balances: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading top balances. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_top_inviters(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Top 30 Inviters (8.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get top inviters
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            top_users = await stats_service.get_top_inviters(30)

            if not top_users:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo users with referrals found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "👥 Top 30 Inviters:\n\n"

            for i, user in enumerate(top_users, 1):
                username = user.get('username', 'N/A')
                first_name = user.get('first_name', 'Unknown')
                total_referrals = user.get('total_referrals', 0)
                referral_earnings = user.get('referral_earnings', 0)
                user_id_str = str(user.get('user_id', 'N/A'))

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Invites: {total_referrals} | Earnings: ₹{referral_earnings:.2f}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='top_inviters')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_top_inviters: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading top inviters. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_top_withdrawers(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Top 30 Withdrawal Rank (8.4)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get top withdrawers
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            top_users = await stats_service.get_top_withdrawers(30)

            if not top_users:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo users with withdrawals found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "🏆 Top 30 Withdrawal Rank:\n\n"

            for i, user in enumerate(top_users, 1):
                username = user.get('username', 'N/A')
                first_name = user.get('first_name', 'Unknown')
                total_withdrawn = user.get('total_withdrawn', 0)
                withdrawal_count = user.get('withdrawal_count', 0)
                user_id_str = str(user.get('user_id', 'N/A'))

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Total: ₹{total_withdrawn:.2f} ({withdrawal_count} withdrawals)\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='top_withdrawers')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_top_withdrawers: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading top withdrawers. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_channel_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Channel Wise Stats (8.5)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get channel statistics
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            channel_stats = await stats_service.get_channel_statistics()

            if not channel_stats:
                await query.edit_message_text(
                    "❌ <b>No Data</b>\n\nNo channel statistics available.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "📈 Channel Wise Stats:\n\n"

            for i, channel in enumerate(channel_stats, 1):
                channel_name = channel.get('channel_name', 'Unknown')
                total_members = channel.get('total_members', 0)
                joined_today = channel.get('joined_today', 0)
                joined_yesterday = channel.get('joined_yesterday', 0)
                joined_this_week = channel.get('joined_this_week', 0)
                joined_this_month = channel.get('joined_this_month', 0)

                message += f"{i}. {channel_name}\n"
                message += f"   Total: {total_members} | Today: {joined_today}\n"
                message += f"   Yesterday: {joined_yesterday} | Week: {joined_this_week}\n"
                message += f"   Month: {joined_this_month}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='channel_stats')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_channel_stats: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading channel statistics. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_pending_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Pending Withdrawals (8.6)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get pending withdrawals
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()
            pending_withdrawals = await stats_service.get_pending_withdrawals()

            if not pending_withdrawals:
                await query.edit_message_text(
                    "✅ <b>No Pending Withdrawals</b>\n\nAll withdrawal requests have been processed.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "⏳ Pending Withdrawals:\n\n"

            for i, withdrawal in enumerate(pending_withdrawals, 1):
                username = withdrawal.get('username', 'N/A')
                first_name = withdrawal.get('first_name', 'Unknown')
                amount = withdrawal.get('amount', 0)
                method = withdrawal.get('withdrawal_method', 'Unknown')
                created_at = withdrawal.get('created_at', 0)
                user_id_str = str(withdrawal.get('user_id', 'N/A'))

                # Format timestamp
                from datetime import datetime
                try:
                    date_str = datetime.fromtimestamp(created_at).strftime("%Y-%m-%d %H:%M")
                except:
                    date_str = "Unknown"

                message += f"{i}. {first_name} (@{username})\n"
                message += f"   ID: {user_id_str} | Amount: ₹{amount:.2f}\n"
                message += f"   Method: {method} | Date: {date_str}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='pending_withdrawals')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_pending_withdrawals: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading pending withdrawals. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_user_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle History Of Users Panel (8.7)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👥 History Of Users\n\nSelect the type of user activity to view:"

            # Build keyboard with all history options
            keyboard = [
                [
                    InlineKeyboardButton('🆕 Recently Joined', callback_data='history_recently_joined_1')
                ],
                [
                    InlineKeyboardButton('💰 Referral Rewards', callback_data='history_referral_rewards_1')
                ],
                [
                    InlineKeyboardButton('🎁 Claimed Gifts', callback_data='history_claimed_gifts_1')
                ],
                [
                    InlineKeyboardButton('💸 Withdrawal History', callback_data='history_withdrawal_history_1')
                ],
                [
                    InlineKeyboardButton('🔗 Custom Link Usage', callback_data='history_custom_link_usage_1')
                ],
                [
                    InlineKeyboardButton('📋 Full User Activity Log', callback_data='history_full_activity_log_1')
                ],
                [
                    InlineKeyboardButton('✅ Task Completion Tracker', callback_data='history_task_completions_1')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_statistics')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_history: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading user history panel. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_user_history_detail(self, update: Update, context: ContextTypes.DEFAULT_TYPE, history_type: str, page: int = 1) -> None:
        """Handle detailed user history with pagination"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get history data
            from services.statistics_service import StatisticsService
            stats_service = StatisticsService()

            history_data, total_records = await stats_service.get_user_history(history_type, page, 15)

            if not history_data:
                await query.edit_message_text(
                    f"❌ <b>No Data</b>\n\nNo {history_type.replace('_', ' ')} records found.",
                    parse_mode='HTML'
                )
                return

            # Calculate pagination
            total_pages = (total_records + 14) // 15  # Ceiling division

            # Build message based on history type
            message = await self._build_history_message(history_type, history_data, page, total_pages, total_records)

            # Build pagination keyboard
            keyboard = await self._build_history_keyboard(history_type, page, total_pages)

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_history_detail: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading history details. Please try again later.",
                parse_mode='HTML'
            )

    async def _build_history_message(self, history_type: str, data: List[Dict[str, Any]], page: int, total_pages: int, total_records: int) -> str:
        """Build history message based on type"""
        try:
            # Title mapping
            titles = {
                "recently_joined": "🆕 Recently Joined Users",
                "referral_rewards": "💰 Referral Rewards",
                "claimed_gifts": "🎁 Claimed Gifts",
                "withdrawal_history": "💸 Withdrawal History",
                "custom_link_usage": "🔗 Custom Link Usage",
                "full_activity_log": "📋 Full User Activity Log",
                "task_completions": "✅ Task Completions"
            }

            title = titles.get(history_type, "User History")
            message = f"{title}\n\n"

            # Add records based on type
            for i, record in enumerate(data, 1):
                if history_type == "recently_joined":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    join_bonus = record.get('joining_bonus_got', 0)  # Use correct field name
                    referred_by = record.get('referred_by', 'Direct')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('created_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Bonus: ₹{join_bonus}\n"
                    message += f"   Referred by: {referred_by} | Date: {date_str}\n\n"

                elif history_type == "referral_rewards":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    earnings = record.get('referral_earnings', 0)
                    referral_count = record.get('referral_count', 0)

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Earnings: ₹{earnings:.2f} ({referral_count} referrals)\n\n"

                elif history_type == "claimed_gifts":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    amount = record.get('amount', 0)
                    code = record.get('code', 'Unknown')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('used_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Amount: ₹{amount}\n"
                    message += f"   Code: {code} | Date: {date_str}\n\n"

                elif history_type == "withdrawal_history":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    amount = record.get('amount', 0)
                    method = record.get('withdrawal_method', 'Unknown')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('completed_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Amount: ₹{amount}\n"
                    message += f"   Method: {method} | Date: {date_str}\n\n"

                elif history_type == "custom_link_usage":
                    link_id = record.get('link_id', 'Unknown')
                    usage_count = record.get('usage_count', 0)
                    total_rewards = record.get('total_rewards', 0)

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('last_used', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. Link: {link_id}\n"
                    message += f"   Usage: {usage_count} | Rewards: ₹{total_rewards}\n"
                    message += f"   Last Used: {date_str}\n\n"

                elif history_type == "full_activity_log":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    action = record.get('action', 'Unknown')
                    details = record.get('details', '')

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('timestamp', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Action: {action}\n"
                    message += f"   Details: {details} | Date: {date_str}\n\n"

                elif history_type == "task_completions":
                    username = record.get('username', 'N/A')
                    first_name = record.get('first_name', 'Unknown')
                    user_id_str = str(record.get('user_id', 'N/A'))
                    task_name = record.get('task_name', 'Unknown')
                    reward = record.get('reward_earned', 0)

                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(record.get('approved_at', 0)).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i}. {first_name} (@{username})\n"
                    message += f"   ID: {user_id_str} | Task: {task_name}\n"
                    message += f"   Reward: ₹{reward} | Date: {date_str}\n\n"

            # Add pagination info
            message += f"\n📄 Page {page} of {total_pages} | Total Records: {total_records}"

            return message

        except Exception as e:
            logger.error(f"Error building history message: {e}")
            return "❌ Error building message"

    async def _build_history_keyboard(self, history_type: str, page: int, total_pages: int) -> List[List[InlineKeyboardButton]]:
        """Build pagination keyboard for history"""
        try:
            keyboard = []

            # Pagination row
            pagination_row = []

            # Previous button
            if page > 1:
                pagination_row.append(
                    InlineKeyboardButton('◀️ Previous', callback_data=f'history_{history_type}_{page-1}')
                )

            # Next button
            if page < total_pages:
                pagination_row.append(
                    InlineKeyboardButton('▶️ Next', callback_data=f'history_{history_type}_{page+1}')
                )

            if pagination_row:
                keyboard.append(pagination_row)

            # Action buttons row
            action_row = [
                InlineKeyboardButton('🔄 Refresh', callback_data=f'history_{history_type}_{page}')
            ]
            keyboard.append(action_row)

            # Back button
            keyboard.append([
                InlineKeyboardButton('↩️ Go Back', callback_data='user_history')
            ])

            return keyboard

        except Exception as e:
            logger.error(f"Error building history keyboard: {e}")
            return [[InlineKeyboardButton('↩️ Go Back', callback_data='user_history')]]

    # ==================== GIFT CODE MANAGEMENT SYSTEM ====================

    async def handle_manage_gift_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Gift Code Management Panel (9.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get gift code statistics
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()
            stats = await gift_service.get_comprehensive_statistics()

            if not stats:
                stats = {
                    'total_codes': 0, 'live_codes': 0, 'used_codes': 0,
                    'total_claims': 0, 'total_amount': 0,
                    'total_link_codes': 0, 'total_link_claims': 0, 'total_link_amount': 0
                }

            # Build message
            message = "⚙️ Hello, Welcome To Code Settings.\n\n"
            message += f"▫️ Total Codes: {stats['total_codes']} (Live: {stats['live_codes']} & Used: {stats['used_codes']})\n\n"
            message += f"▫️ Total Claims: {stats['total_claims']}\n"
            message += f"▫️ Total Balance Added: ₹{stats['total_amount']:.2f}\n\n"
            message += f"▫️ Total Codes Created By Link: {stats['total_link_codes']}\n"
            message += f"▫️ Total Link Claims: {stats['total_link_claims']}\n"
            message += f"▫️ Total Link Amounts: ₹{stats['total_link_amount']:.2f}"

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton('➕ Add Redeem Code', callback_data='add_redeem_code')
                ],
                [
                    InlineKeyboardButton('🟢 Live Codes', callback_data='live_codes')
                ],
                [
                    InlineKeyboardButton('🔴 Used Codes', callback_data='used_codes')
                ],
                [
                    InlineKeyboardButton('🔗 Link Based Redeem', callback_data='link_based_redeem')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_gift_codes: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading gift code management. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_add_redeem_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Add Redeem Code workflow (9.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add Redeem Code</b>\n\n"
            message += "Send the code details as mentioned below:\n\n"
            message += "min-max,totalUsers,code (e.g., 10-50,80,PROMO)\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Gift Code Management', callback_data='manage_gift_codes')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_redeem_code')

        except Exception as e:
            logger.error(f"Error in handle_add_redeem_code: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_live_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Live Codes Display (9.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get live codes
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()
            live_codes = await gift_service.get_live_codes_detailed()

            if not live_codes:
                await query.edit_message_text(
                    "🟢 <b>Live Codes</b>\n\nNo active codes found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "🟢 <b>Live Codes</b>\n\n"

            for i, code in enumerate(live_codes, 1):
                code_name = code.get('code', 'Unknown')

                # Handle both admin gift codes and traditional gift codes
                if 'is_active' in code:
                    # Admin gift code (new system)
                    min_amount = code.get('min_amount', 0)
                    max_amount = code.get('max_amount', 0)
                    usage_limit = code.get('usage_limit', 0)
                    usage_count = code.get('usage_count', 0)
                    amount_display = f"₹{min_amount}-₹{max_amount}"
                else:
                    # Traditional gift code (PHP migration)
                    amount = code.get('amount', 0)
                    usage_limit = code.get('usage_limit', 0)
                    usage_count = code.get('used_count', 0)  # Traditional codes use 'used_count'
                    amount_display = f"₹{amount}"

                remaining = usage_limit - usage_count if usage_limit > 0 else "∞"

                from datetime import datetime
                try:
                    date_str = datetime.fromtimestamp(code.get('created_at', 0)).strftime("%Y-%m-%d")
                except:
                    date_str = "Unknown"

                message += f"{i}. <b>{code_name}</b>\n"
                message += f"   Amount: {amount_display}\n"
                message += f"   Usage: {usage_count}/{usage_limit if usage_limit > 0 else '∞'} (Remaining: {remaining})\n"
                message += f"   Created: {date_str}\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='live_codes')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_live_codes: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading live codes. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_used_codes(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Used Codes Display (9.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get used codes
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()
            used_codes = await gift_service.get_used_codes_detailed()

            if not used_codes:
                await query.edit_message_text(
                    "🔴 <b>Used Codes</b>\n\nNo completed codes found.",
                    parse_mode='HTML'
                )
                return

            # Build message
            message = "🔴 <b>Used Codes</b>\n\n"

            for i, code in enumerate(used_codes, 1):
                code_name = code.get('code', 'Unknown')
                created_by = code.get('created_by', 0)

                # Handle both admin and traditional gift codes
                if 'is_active' in code:
                    # Admin gift code (new system)
                    min_amount = code.get('min_amount', 0)
                    max_amount = code.get('max_amount', 0)
                    usage_limit = code.get('usage_limit', 0)
                    usage_count = code.get('usage_count', 0)
                    users_claimed = code.get('users_claimed', [])

                    # Calculate total distributed (approximate)
                    avg_amount = (min_amount + max_amount) / 2
                    total_distributed = avg_amount * usage_count

                    message += f"🎁 Redeem Code: <b>{code_name}</b>\n"
                    message += f"▫️ Created by Admin: {created_by}\n"
                    message += f"▫️ Total Users: {usage_limit}\n"
                    message += f"▫️ Claim Amount: ₹{min_amount}-₹{max_amount}\n"
                    message += f"▫️ Claimed: {usage_count}\n"
                    message += f"▫️ Balance Added: ₹{total_distributed:.2f}\n"

                    # Show who redeemed it
                    if users_claimed:
                        message += f"▫️ Redeemed by: {len(users_claimed)} users\n"

                else:
                    # Traditional gift code (PHP migration)
                    amount = code.get('amount', 0)
                    usage_limit = code.get('usage_limit', 0)
                    used_count = code.get('used_count', 0)
                    redeemed_by = code.get('redeemed_by', [])

                    total_distributed = amount * used_count

                    message += f"🎁 Redeem Code: <b>{code_name}</b>\n"
                    message += f"▫️ Created by Admin: {created_by}\n"
                    message += f"▫️ Total Users: {usage_limit}\n"
                    message += f"▫️ Claim Amount: ₹{amount}\n"
                    message += f"▫️ Claimed: {used_count}\n"
                    message += f"▫️ Balance Added: ₹{total_distributed:.2f}\n"

                    # Show who redeemed it
                    if redeemed_by:
                        message += f"▫️ Redeemed by: {len(redeemed_by)} users\n"

                message += f"🔴 Status: Stopped\n\n"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Refresh', callback_data='used_codes')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_used_codes: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading used codes. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_link_based_redeem(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Link Based Redeem System (9.4) - Redesigned Two-Button Layout"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🔗 <b>Link Based Redeem</b>\n\n"
            message += "Choose the type of redemption link you want to create:\n\n"
            message += "💰 <b>Fixed Amount:</b> All users receive the same amount\n"
            message += "🎲 <b>Random Amount:</b> Users receive random amounts within a range\n\n"
            message += "Select an option to start the link creation process:"

            # Simplified two-button layout
            keyboard = [
                [
                    InlineKeyboardButton('💰 Fixed Amount', callback_data='link_fixed_amount'),
                    InlineKeyboardButton('🎲 Random Amount', callback_data='link_random_amount')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_link_based_redeem: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading link-based redeem. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_link_fixed_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Fixed Amount workflow (9.4.1) - New Streamlined Process"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "💰 <b>Fixed Amount Link</b>\n\n"
            message += "📝 <b>Step 1 of 3:</b> Enter the fixed amount\n\n"
            message += "All users who claim this link will receive the same amount.\n\n"
            message += "💡 <b>Example:</b> 50 (for ₹50 per user)\n\n"
            message += "💰 <b>Enter the amount:</b>"

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for new workflow
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'link_fixed_amount_step1')

        except Exception as e:
            logger.error(f"Error in handle_link_fixed_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_link_random_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Random Amount workflow (9.4.2) - New Streamlined Process"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            # Use async admin check for better reliability
            from utils.helpers import is_admin_async
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🎲 <b>Random Amount Link</b>\n\n"
            message += "📝 <b>Step 1 of 3:</b> Enter the amount range\n\n"
            message += "Users will receive random amounts within your specified range.\n\n"
            message += "💡 <b>Format:</b> MIN-MAX\n"
            message += "💡 <b>Example:</b> 10-100 (for ₹10 to ₹100 range)\n\n"
            message += "🎲 <b>Enter the range:</b>"

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session for new workflow
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'link_random_amount_step1')

        except Exception as e:
            logger.error(f"Error in handle_link_random_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_link_user_limit(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Total Users workflow (9.4.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👥 <b>Total Users Limit</b>\n\n"
            message += "Send total number of users who can claim this link:\n\n"
            message += "Example: 100\n"
            message += "Send 'unlimited' for no limit\n\n"
            message += "Send /cancel to cancel."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'link_user_limit')

        except Exception as e:
            logger.error(f"Error in handle_link_user_limit: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_generate_redeem_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Generate Link workflow (9.4.4)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get link settings from session using enhanced method
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()

            # Get link redeem configuration data
            link_settings = await session_handlers.get_link_redeem_config(user_id)

            amount_type = link_settings.get('amount_type')
            amount_value = link_settings.get('amount_value')
            user_limit = link_settings.get('user_limit', -1)

            # Validate that amount type is selected
            if not amount_type or not amount_value:
                await query.edit_message_text(
                    "❌ <b>Configuration Required</b>\n\nPlease set the amount type (Fixed or Random) first.",
                    parse_mode='HTML'
                )
                return

            # Generate unique code and create link
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()

            code = await gift_service.generate_link_code()
            if not code:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to generate unique code. Please try again.",
                    parse_mode='HTML'
                )
                return

            # Create link-based code in database
            success = await gift_service.create_link_based_code(
                code=code,
                amount_type=amount_type,
                amount_value=amount_value,
                user_limit=user_limit,
                created_by=user_id
            )

            if not success:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nFailed to create redeem link. Please try again.",
                    parse_mode='HTML'
                )
                return

            # Get bot username for link generation
            from config.settings import settings
            bot_username = settings.BOT_USERNAME

            # Build success message with details
            message = "🔗 <b>Redeem Link Generated Successfully!</b>\n\n"
            message += f"📝 <b>Code:</b> <code>{code}</code>\n"

            if amount_type == 'fixed':
                message += f"💰 <b>Amount:</b> ₹{amount_value} per user\n"
            else:
                message += f"🎲 <b>Amount:</b> ₹{amount_value['min']}-₹{amount_value['max']} range\n"

            if user_limit == -1:
                message += f"👥 <b>User Limit:</b> Unlimited\n"
            else:
                message += f"👥 <b>User Limit:</b> {user_limit} users\n"

            message += f"\n🔗 <b>Link:</b>\n<code>https://t.me/{bot_username}?start=redeem_{code}</code>\n\n"
            message += "📤 <b>Share this link with users to claim rewards!</b>\n\n"
            message += "💡 <b>Usage Instructions:</b>\n"
            message += "• Users click the link to start the bot\n"
            message += "• Rewards are automatically credited to their wallet\n"
            message += "• Each user can only claim once per link"

            keyboard = [
                [
                    InlineKeyboardButton('🔄 Generate Another', callback_data='link_based_redeem')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

            # Clear session after successful generation
            await session_handlers.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in handle_generate_redeem_link: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while generating link. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== FORCESUB CHANNELS MANAGEMENT SYSTEM ====================

    async def handle_forcesub_channels(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle ForceSub Channels Management Panel (10.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get force subscribe statistics and channels
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService(bot=context.bot)
            message = await force_service.format_admin_channels_message()

            # Build keyboard with improved layout
            keyboard = [
                # Main Channel - Single wide button
                [
                    InlineKeyboardButton('🏡 Set Main Channel', callback_data='set_main_channel')
                ],
                # Channel Management - Horizontal pair
                [
                    InlineKeyboardButton('➕ Add Channel', callback_data='add_forcesub_channel'),
                    InlineKeyboardButton('➖ Remove Channel', callback_data='remove_forcesub_channel')
                ],
                # Navigation - Single button
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_forcesub_channels: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading ForceSub channels. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_set_main_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Set Main Channel workflow (10.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🏡 <b>Set Main Channel</b>\n\n"
            message += "Send the main channel username or invite link:\n\n"
            message += "(e.g., @channelname or https://t.me/+invitelink)\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to ForceSub Channels', callback_data='forcesub_channels')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_main_channel')

        except Exception as e:
            logger.error(f"Error in handle_set_main_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_add_forcesub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Add Channel workflow (10.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "➕ <b>Add Force Subscribe Channel</b>\n\n"
            message += "To add a new force subscription channel:\n\n"
            message += "1️⃣ <b>Make the bot an admin</b> in the target channel first\n"
            message += "2️⃣ <b>Forward any message</b> from that channel to this bot\n"
            message += "3️⃣ I will <b>automatically verify</b> and add the channel\n\n"
            message += "⚠️ <b>Requirements:</b>\n"
            message += "• The bot must be an administrator in the channel\n"
            message += "• The channel must be public or the bot must have access\n\n"
            message += "📤 <b>Please forward a message from the channel now:</b>\n\n"
            message += "Send /cancel to cancel."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to ForceSub Channels', callback_data='forcesub_channels')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_forcesub_channel')

        except Exception as e:
            logger.error(f"Error in handle_add_forcesub_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_forcesub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Remove Channel workflow (10.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get channels for removal
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService(bot=context.bot)
            channels_for_removal = await force_service.get_channels_for_removal()

            if not channels_for_removal:
                # Add back button for navigation
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to ForceSub Channels', callback_data='forcesub_channels')]
                ])

                await query.edit_message_text(
                    "➖ <b>Remove Channel</b>\n\n❌ No channels configured to remove.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Build message with numbered channels
            message = "➖ <b>Remove Channel</b>\n\n"
            message += "Select channel number to remove:\n\n"

            for channel_info in channels_for_removal:
                message += f"{channel_info['number']}. {channel_info['display_name']}\n"

            message += "\nSend the number or /cancel to cancel."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to ForceSub Channels', callback_data='forcesub_channels')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_forcesub_channel')

        except Exception as e:
            logger.error(f"Error in handle_remove_forcesub_channel: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== GIFT BROADCAST SYSTEM ====================

    async def handle_gift_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Gift Broadcast Panel (11.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Start gift broadcast creation flow
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()

            # Set user session for gift broadcast forward step
            await session_handlers.set_user_session(user_id, 'gift_broadcast_forward', {})

            await query.edit_message_text(
                "🎁 <b>Create Gift Broadcast</b>\n\n"
                "Please forward a message from the channel you want to use for the gift broadcast.\n\n"
                "💡 <b>How to forward:</b>\n"
                "1. Go to your target channel\n"
                "2. Select any message from that channel\n"
                "3. Tap 'Forward'\n"
                "4. Send it to this bot\n\n"
                "Send /cancel to cancel the process.",
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_gift_broadcast: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_start_gift_broadcast(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle starting a gift broadcast to all users"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Extract broadcast ID from callback data
            broadcast_id = query.data.replace('start_gift_broadcast_', '')

            # Get gift broadcast data
            from services.gift_broadcast_service import GiftBroadcastService
            gift_service = GiftBroadcastService(bot=context.bot)

            broadcast_data = await gift_service.get_gift_broadcast(broadcast_id)
            if not broadcast_data:
                await query.edit_message_text(
                    "❌ <b>Error</b>\n\nGift broadcast not found.",
                    parse_mode='HTML'
                )
                return

            # Add admin_id to broadcast_data for the optimized system
            broadcast_data['admin_id'] = user_id

            # Start the broadcast
            success, message = await gift_service.start_gift_broadcast_to_all_users(broadcast_data)

            if success:
                await query.edit_message_text(
                    f"✅ <b>Gift Broadcast Started!</b>\n\n"
                    f"📢 Broadcasting to all bot users with real-time progress tracking...\n"
                    f"💰 Reward: ₹{broadcast_data['reward_amount']} per user\n"
                    f"📊 {message}\n\n"
                    f"💡 You will receive real-time progress updates and detailed statistics during the broadcast.\n"
                    f"🚫 Send /cancel to stop the broadcast if needed.",
                    parse_mode='HTML'
                )
            else:
                await query.edit_message_text(
                    f"❌ <b>Broadcast Failed</b>\n\n{message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_start_gift_broadcast: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

            message = "🎁 <b>Gift Broadcast - Channel Selection</b>\n\n"
            message += "Create a channel-based reward campaign where users join a channel to receive rewards.\n\n"
            message += "📋 <b>How it works:</b>\n"
            message += "1️⃣ Select a channel for the campaign\n"
            message += "2️⃣ Set the reward amount per user\n"
            message += "3️⃣ Bot broadcasts the campaign to all users\n"
            message += "4️⃣ Users join the channel and claim rewards\n\n"
            message += "🔧 <b>Easy Channel Setup:</b>\n"
            message += "1️⃣ First, add this bot as an administrator to your target channel\n"
            message += "2️⃣ Then, forward any message from that channel to this bot\n"
            message += "3️⃣ The bot will automatically detect and configure the channel\n\n"
            message += "💡 <b>This works for both public and private channels!</b>\n\n"
            message += "⚠️ <b>Requirements:</b>\n"
            message += "✅ The bot must be an admin in the target channel\n"
            message += "✅ You must have access to forward messages from the channel\n\n"
            message += "📤 <b>Please forward a message from your target channel now:</b>"

            keyboard = [
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

            # Set user session for forwarded message input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'gift_broadcast_forward')

        except Exception as e:
            logger.error(f"Error in handle_gift_broadcast: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Gift Broadcast. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== BOT MAINTENANCE SYSTEM ====================

    async def handle_bot_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Bot Maintenance Panel (12.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get maintenance status and format message
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            message = await maintenance_service.format_maintenance_status_message()

            # Get current status for button labels
            status = await maintenance_service.get_maintenance_status()
            general_toggle = "ON⬆️" if status["general_maintenance"] else "OFF⬇️"

            # Build keyboard
            keyboard = [
                [
                    InlineKeyboardButton(f'🔧 General Maintenance {general_toggle}', callback_data='toggle_general_maintenance')
                ],
                [
                    InlineKeyboardButton('💵 Withdrawal Maintenance', callback_data='withdrawal_maintenance')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_bot_maintenance: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Bot Maintenance. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_toggle_general_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle General Maintenance Toggle (12.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle general maintenance
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            success, message = await maintenance_service.toggle_general_maintenance(user_id)

            if success:
                # Refresh the maintenance panel
                await self.handle_bot_maintenance(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_general_maintenance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_withdrawal_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Withdrawal Maintenance Panel (12.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get withdrawal maintenance status and format message
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            message = await maintenance_service.format_withdrawal_maintenance_message()

            # Get current status for button labels
            status = await maintenance_service.get_maintenance_status()
            bank_toggle = "ON⬆️" if status["bank_withdrawal_maintenance"] else "OFF⬇️"
            usdt_toggle = "ON⬆️" if status["usdt_withdrawal_maintenance"] else "OFF⬇️"

            # Build keyboard with horizontal layout
            keyboard = [
                [
                    InlineKeyboardButton(f'🏦 Bank {bank_toggle}', callback_data='toggle_bank_maintenance')
                ],
                [
                    InlineKeyboardButton(f'💲 USDT {usdt_toggle}', callback_data='toggle_usdt_maintenance')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='bot_maintenance')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_withdrawal_maintenance: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Withdrawal Maintenance. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_toggle_bank_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Bank Withdrawal Maintenance Toggle (12.2.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle bank withdrawal maintenance
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            success, message = await maintenance_service.toggle_bank_withdrawal_maintenance(user_id)

            if success:
                # Refresh the withdrawal maintenance panel
                await self.handle_withdrawal_maintenance(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_bank_maintenance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_usdt_maintenance(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle USDT Withdrawal Maintenance Toggle (12.2.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle USDT withdrawal maintenance
            from services.maintenance_service import MaintenanceService
            maintenance_service = MaintenanceService()
            success, message = await maintenance_service.toggle_usdt_withdrawal_maintenance(user_id)

            if success:
                # Refresh the withdrawal maintenance panel
                await self.handle_withdrawal_maintenance(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_usdt_maintenance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== CUSTOM REFERRAL LINK SYSTEM ====================

    async def handle_custom_referral_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Custom Referral Link Panel (13.0)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get custom referral status and format message
            from services.custom_referral_service import CustomReferralService
            referral_service = CustomReferralService()
            message = await referral_service.format_admin_panel_message()

            # Get current status for button labels
            stats = await referral_service.get_admin_statistics()
            toggle_text = "❌ Turn Off" if stats["system_enabled"] else "✅ Turn On"

            # Build keyboard with improved layout
            keyboard = [
                # System Toggle - Single wide button
                [
                    InlineKeyboardButton(toggle_text, callback_data='toggle_custom_referral')
                ],
                # Link Management - Horizontal pair
                [
                    InlineKeyboardButton('🗑 Delete Invite Link', callback_data='delete_invite_link'),
                    InlineKeyboardButton('📋 Copy My Link', callback_data='copy_admin_link')
                ],
                # Navigation - Single button
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_link: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while loading Custom Referral Link. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_toggle_custom_referral(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Custom Referral Toggle (13.1)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Toggle custom referral system
            from services.custom_referral_service import CustomReferralService
            referral_service = CustomReferralService()
            success, message = await referral_service.toggle_custom_referral_system(user_id)

            if success:
                # Refresh the custom referral panel
                await self.handle_custom_referral_link(update, context)
            else:
                await query.edit_message_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_toggle_custom_referral: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_invite_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Delete Invite Link workflow (13.2)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🗑 <b>Delete Invite Link</b>\n\n"
            message += "Send the custom parameter or user ID of the link to delete:\n\n"
            message += "Examples:\n"
            message += "• from_whatsapp (parameter)\n"
            message += "• ********* (user ID)\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'delete_invite_link')

        except Exception as e:
            logger.error(f"Error in handle_delete_invite_link: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_copy_admin_link(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle Copy My Link (13.3)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get admin's custom link
            from services.custom_referral_service import CustomReferralService
            referral_service = CustomReferralService()

            # Check if admin has a custom link
            has_link = await referral_service.user_has_custom_link(user_id)

            if has_link:
                # Get the link
                custom_link = await referral_service.get_user_custom_link(user_id)
                if custom_link:
                    message = f"📋 <b>Your Custom Referral Link</b>\n\n"
                    message += f"🔗 Link: {custom_link}\n\n"
                    message += f"You can copy and share this link to earn referral rewards!"
                else:
                    message = "❌ Error retrieving your custom link."
            else:
                # Admin doesn't have a custom link
                message = "📋 <b>No Custom Link Found</b>\n\n"
                message += "You don't have a custom referral link yet.\n\n"
                message += "💡 Use /customref create [parameter] to create one!"

            keyboard = [
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='custom_referral_link')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_copy_admin_link: {e}")
            await query.edit_message_text(
                "❌ <b>Error</b>\n\nSomething went wrong while retrieving your link. Please try again later.",
                parse_mode='HTML'
            )

    async def handle_custom_referral_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /customref command"""
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await update.message.reply_text(ADMIN_ACCESS_DENIED, parse_mode='HTML')
                return

            # Parse command arguments
            text = update.message.text
            parts = text.split(' ')

            if len(parts) < 2:
                await self._show_custom_referral_help(update)
                return

            command = parts[1]
            parameters = parts[2:] if len(parts) > 2 else []

            # Route to appropriate handler
            if command == 'list':
                await self._handle_custom_referral_list(update)
            elif command == 'create' and len(parameters) >= 2:
                await self._handle_custom_referral_create(update, parameters[0], parameters[1])
            elif command == 'edit' and len(parameters) >= 2:
                await self._handle_custom_referral_edit(update, parameters[0], parameters[1])
            elif command == 'delete' and len(parameters) >= 1:
                await self._handle_custom_referral_delete(update, parameters[0])
            elif command == 'view' and len(parameters) >= 1:
                await self._handle_custom_referral_view(update, parameters[0])
            else:
                await self._show_custom_referral_help(update)

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_command: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== USER BONUS MANAGEMENT PANEL ====================

    async def handle_user_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle User Bonus Management Panel"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current admin settings
            admin_settings = await self.admin_service.get_admin_settings()

            # Get level rewards status using dedicated service
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()
            level_rewards_enabled = await level_rewards_service.is_level_rewards_enabled()

            # Get other bonus settings
            invite_bonus_range = admin_settings.get('per_refer_amount_range', '1-5')
            new_user_bonus_range = admin_settings.get('joining_bonus_amount_range', '4-6')

            # Build message
            message = "⚙️ Hello, Welcome To The Bonus Settings.\n\n"
            message += f"▫️ Extra Level Bonus: {'🟢 Enabled' if level_rewards_enabled else '🔴 Disabled'}\n"
            message += f"▫️ Invite Bonus: {invite_bonus_range}\n"
            message += f"▫️ New User Bonus: {new_user_bonus_range}\n"

            # Build keyboard
            level_toggle_text = "🔴 Disable Level Bonus" if level_rewards_enabled else "🟢 Enable Level Bonus"

            keyboard = [
                [
                    InlineKeyboardButton(level_toggle_text, callback_data='toggle_level_bonus')
                ],
                [
                    InlineKeyboardButton('👤 New User Bonus', callback_data='set_new_user_bonus')
                ],
                [
                    InlineKeyboardButton('🎯 Invite Bonus', callback_data='set_invite_bonus')
                ],
                [
                    InlineKeyboardButton('🏆 Level Rewards Configuration', callback_data='configure_level_rewards')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_level_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Toggle level bonus system on/off"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Get current status using dedicated service
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()

            current_status = await level_rewards_service.is_level_rewards_enabled()
            new_status = not current_status

            # Update database using dedicated service
            success = await level_rewards_service.toggle_level_rewards(new_status)

            if success:
                status_text = "🟢 Level Bonus has been ENABLED" if new_status else "🔴 Level Bonus has been DISABLED"
                await query.answer(status_text, show_alert=True)

                # Refresh the bonus panel
                await self.handle_user_bonus(update, context)
            else:
                await query.answer("❌ Failed to update level bonus status.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_toggle_level_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_new_user_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set new user bonus range"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👤 <b>New User Bonus Range</b>\n\n"
            message += "Send the new user bonus range (e.g., 40-51):\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_new_user_bonus')

        except Exception as e:
            logger.error(f"Error in handle_set_new_user_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_invite_bonus(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Set invite bonus range"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "🎯 <b>Invite Bonus Range</b>\n\n"
            message += "Send the invite bonus range (e.g., 1-3):\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_invite_bonus')

        except Exception as e:
            logger.error(f"Error in handle_set_invite_bonus: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)



    # ==================== USER DETAILS & SETTINGS PANEL ====================

    async def handle_user_details_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle User Details & Settings Panel - Initial prompt for user ID"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "👤 <b>User Details & Settings</b>\n\n"
            message += "Send user's telegram Chat Id :-\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin_panel')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'user_details_get_id')

        except Exception as e:
            logger.error(f"Error in handle_user_details_settings: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_show_user_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Show user details and management options"""
        query = update.callback_query if update.callback_query else None
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                if query:
                    await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Get user details
            from services.user_service import UserService
            user_service = UserService()
            user_data = await user_service.get_user(target_user_id)

            if not user_data:
                error_msg = "❌ User not found in database."
                if query:
                    await query.edit_message_text(error_msg)
                else:
                    await update.message.reply_text(error_msg)
                return

            # Get additional user information
            username = user_data.get('username', '')
            display_username = f"@{username}" if username else "User"
            referred_by = user_data.get('referred_by', 'None')
            balance = user_data.get('balance', 0)
            total_withdrawn = user_data.get('successful_withdraw', 0)
            banned = user_data.get('banned', False)

            # Get referral count and earnings from promotion_report (actual referrals with earnings)
            promotion_report = user_data.get('promotion_report', [])
            referral_count = len(promotion_report)
            total_referral_earnings = sum(report.get('amount_got', 0) for report in promotion_report)

            # Calculate total subordinates using promotion report service
            from services.promotion_report_service import PromotionReportService
            promotion_service = PromotionReportService()
            total_subordinates = await promotion_service._calculate_total_subordinates(target_user_id)

            # Get total bonus claimed (balance + withdrawn)
            total_bonus_claimed = balance + total_withdrawn

            # Get withdrawal method details from account_info
            account_info = user_data.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', '')

            # Format withdrawal method information
            if withdrawal_method == 'bank':
                account_number = account_info.get('account_number', '')
                ifsc = account_info.get('ifsc', '')
                name = account_info.get('name', '')
                if account_number and ifsc:
                    withdrawal_info = f"🏦 Bank: {name} | A/C: {account_number} | IFSC: {ifsc}"
                else:
                    withdrawal_info = "🏦 Bank (Incomplete Setup)"
            elif withdrawal_method == 'usdt':
                usdt_address = account_info.get('usdt_address', '')
                binance_id = account_info.get('binance_id', '')
                if usdt_address:
                    withdrawal_info = f"₿ USDT: {usdt_address[:10]}...{usdt_address[-6:] if len(usdt_address) > 16 else usdt_address}"
                    if binance_id:
                        withdrawal_info += f" | Binance: {binance_id}"
                else:
                    withdrawal_info = "₿ USDT (Incomplete Setup)"
            else:
                withdrawal_info = "❌ Not Set"

            # Build user details message
            message = f"👤 <b>User Details</b>\n\n"
            message += f"User: {display_username}\n"
            message += f"Invited by: {referred_by}\n"
            message += f"Balance: ₹{balance}\n"
            message += f"Withdrawn: ₹{total_withdrawn}\n"
            message += f"Total Invites: {referral_count}\n"
            message += f"Total Subordinates: {total_subordinates}\n"
            message += f"Referral Earnings: ₹{total_referral_earnings}\n"
            message += f"Current Status: ₹{total_bonus_claimed}\n"
            message += f"Bank/USDT: {withdrawal_info}\n"

            # Build keyboard with dynamic ban/unban button
            ban_text = "✅ Unban User" if banned else "⚠️ Ban User"
            ban_callback = f"unban_user_{target_user_id}" if banned else f"ban_user_{target_user_id}"

            keyboard = [
                # User Status - Single wide button
                [
                    InlineKeyboardButton(ban_text, callback_data=ban_callback)
                ],
                # Records - Horizontal pair
                [
                    InlineKeyboardButton('👥 Invites Record', callback_data=f'user_invites_{target_user_id}'),
                    InlineKeyboardButton('💸 Withdrawal Records', callback_data=f'user_withdrawals_{target_user_id}')
                ],
                # Balance Management - Horizontal pair (already good)
                [
                    InlineKeyboardButton('➕ Add Balance', callback_data=f'add_balance_{target_user_id}'),
                    InlineKeyboardButton('➖ Remove Balance', callback_data=f'remove_balance_{target_user_id}')
                ],
                # Additional Records & Communication - Horizontal pair
                [
                    InlineKeyboardButton('📋 Task Records', callback_data=f'user_tasks_{target_user_id}'),
                    InlineKeyboardButton('📨 Send Message', callback_data=f'send_message_{target_user_id}')
                ],
                # Navigation - Single button
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')
                ]
            ]

            if query:
                await query.edit_message_text(
                    text=message,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    text=message,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_show_user_details: {e}")
            error_msg = "❌ Something went wrong. Please try again later."
            if query:
                await query.answer(error_msg, show_alert=True)
            else:
                await update.message.reply_text(error_msg)

    async def handle_user_ban_toggle(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int, ban_action: bool) -> None:
        """Ban or unban a user"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Update user ban status
            from services.user_service import UserService
            user_service = UserService()
            success = await user_service.ban_user(target_user_id, ban_action)

            if success:
                action_text = "banned" if ban_action else "unbanned"
                await query.answer(f"✅ User has been {action_text}.", show_alert=True)

                # Refresh user details
                await self.handle_show_user_details(update, context, target_user_id)
            else:
                await query.answer("❌ Failed to update user status.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_user_ban_toggle: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_user_invites_record(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int, page: int = 1) -> None:
        """Show user's invites record with pagination"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get user's referral data from promotion_report
            from services.user_service import UserService
            user_service = UserService()
            user_data = await user_service.get_user(target_user_id)

            if not user_data:
                message = "❌ <b>User not found</b>"
                keyboard = [[InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')]]
                await query.edit_message_text(
                    text=message,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode='HTML'
                )
                return

            # Get promotion report (actual referral data with earnings)
            promotion_report = user_data.get('promotion_report', [])

            # Calculate total subordinates using promotion report service
            from services.promotion_report_service import PromotionReportService
            promotion_service = PromotionReportService()
            total_subordinates = await promotion_service._calculate_total_subordinates(target_user_id)

            # Pagination settings
            per_page = 12
            total_referrals = len(promotion_report)
            total_pages = max(1, (total_referrals + per_page - 1) // per_page)
            page = max(1, min(page, total_pages))

            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            page_referrals = promotion_report[start_idx:end_idx]

            # Build message
            user_name = user_data.get('first_name', 'Unknown')
            message = f"👥 <b>Invites Record - {user_name}</b>\n\n"
            message += f"📊 <b>Total Referrals:</b> {total_referrals}\n"
            message += f"🌐 <b>Total Subordinates:</b> {total_subordinates}\n\n"

            if not page_referrals:
                message += "⚠️ User has no referrals yet."
            else:
                message += f"<b>Page {page} of {total_pages}</b>\n\n"

                for i, referral in enumerate(page_referrals, start_idx + 1):
                    referred_name = referral.get('referred_user_name', 'Unknown')
                    referred_id = referral.get('referred_user_id', 'N/A')
                    amount_earned = referral.get('amount_got', 0)

                    # Get proper username display using the same logic as promotion reports
                    username_display = await promotion_service._get_proper_username_display(referred_id, referred_name)

                    message += f"{i:02d}. {referred_id} [{username_display}] - ₹{amount_earned}\n"

            # Build keyboard
            keyboard = []

            # Navigation buttons
            nav_buttons = []
            if page > 1:
                nav_buttons.append(InlineKeyboardButton('⬅️ Previous', callback_data=f'user_invites_{target_user_id}_{page-1}'))
            if page < total_pages:
                nav_buttons.append(InlineKeyboardButton('➡️ Next', callback_data=f'user_invites_{target_user_id}_{page+1}'))

            if nav_buttons:
                keyboard.append(nav_buttons)

            keyboard.append([InlineKeyboardButton('↩️ Go Back', callback_data=f'show_user_{target_user_id}')])

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_invites_record: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_user_balance_operation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int, operation: str) -> None:
        """Handle add/remove balance operations"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            operation_text = "add" if operation == "add" else "remove"
            message = f"💰 <b>{operation_text.title()} Balance</b>\n\n"
            message += f"Enter amount to {operation_text} {'to' if operation == 'add' else 'from'} user's balance:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, f'{operation}_user_balance', {'target_user_id': target_user_id})

        except Exception as e:
            logger.error(f"Error in handle_user_balance_operation: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_send_user_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int) -> None:
        """Send message to user"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📨 <b>Send Message to User</b>\n\n"
            message += "Enter the message to send to user :-\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'send_user_message', {'target_user_id': target_user_id})

        except Exception as e:
            logger.error(f"Error in handle_send_user_message: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_user_task_records(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int, page: int = 1) -> None:
        """Show user's task completion records with pagination"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get user data for name
            from services.user_service import UserService
            user_service = UserService()
            user_data = await user_service.get_user(target_user_id)

            if not user_data:
                message = "❌ <b>User not found</b>"
                keyboard = [[InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')]]
                await query.edit_message_text(
                    text=message,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode='HTML'
                )
                return

            # Get user's task submissions with detailed information
            from services.task_service import TaskService
            task_service = TaskService()
            task_submissions = await task_service.get_user_task_submissions(target_user_id)

            # Get task details for each submission
            detailed_submissions = []
            for submission in task_submissions:
                task_id = submission.get('task_id')
                task_details = await task_service.get_task_by_id(task_id)

                if task_details:
                    submission_info = {
                        'task_name': task_details.get('name', 'Unknown Task'),
                        'reward_amount': task_details.get('reward_amount', 0),
                        'status': submission.get('status', 'pending'),
                        'submitted_at': submission.get('submitted_at', 0),
                        'reviewed_at': submission.get('reviewed_at', 0),
                        'admin_note': submission.get('admin_note', '')
                    }
                    detailed_submissions.append(submission_info)

            # Pagination settings
            per_page = 10
            total_submissions = len(detailed_submissions)
            total_pages = max(1, (total_submissions + per_page - 1) // per_page)
            page = max(1, min(page, total_pages))

            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            page_submissions = detailed_submissions[start_idx:end_idx]

            # Build message
            user_name = user_data.get('first_name', 'Unknown')
            message = f"📋 <b>Task Records - {user_name}</b>\n\n"
            message += f"📊 <b>Total Tasks Completed:</b> {total_submissions}\n\n"

            if not page_submissions:
                message += "⚠️ User has not completed any tasks yet."
            else:
                message += f"<b>Page {page} of {total_pages}</b>\n\n"

                for i, submission in enumerate(page_submissions, start_idx + 1):
                    task_name = submission['task_name']
                    reward = submission['reward_amount']
                    status = submission['status']

                    # Format status with emoji
                    status_emoji = {
                        'approved': '✅',
                        'pending': '⏳',
                        'rejected': '❌'
                    }.get(status, '❓')

                    # Format date
                    from datetime import datetime
                    try:
                        if submission['reviewed_at'] and submission['reviewed_at'] > 0:
                            date_timestamp = submission['reviewed_at']
                        else:
                            date_timestamp = submission['submitted_at']
                        date_str = datetime.fromtimestamp(date_timestamp).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i:02d}. <b>{task_name}</b>\n"
                    message += f"    Status: {status_emoji} {status.title()}\n"
                    message += f"    Reward: ₹{reward} | Date: {date_str}\n\n"

            # Build keyboard
            keyboard = []

            # Navigation buttons
            nav_buttons = []
            if page > 1:
                nav_buttons.append(InlineKeyboardButton('⬅️ Previous', callback_data=f'user_tasks_{target_user_id}_{page-1}'))
            if page < total_pages:
                nav_buttons.append(InlineKeyboardButton('➡️ Next', callback_data=f'user_tasks_{target_user_id}_{page+1}'))

            if nav_buttons:
                keyboard.append(nav_buttons)

            keyboard.append([InlineKeyboardButton('↩️ Go Back', callback_data=f'show_user_{target_user_id}')])

            await query.edit_message_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_user_task_records: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_manage_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle manage withdrawals menu"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get withdrawal settings
            admin_settings = await self.admin_service.get_admin_settings()
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})
            withdrawal_enabled = withdrawal_settings.get('enabled', True)

            # Create withdrawal management message
            message = "💰 <b>Withdrawal Management</b>\n\n"
            message += f"📊 <b>Current Status:</b>\n"
            message += f"• Withdrawal Status: {'🟢 Enabled' if withdrawal_enabled else '🔴 Disabled'}\n"
            message += f"• Tax Type: {withdrawal_settings.get('tax_type', 'none').title()}\n"

            if withdrawal_settings.get('tax_type') == 'percentage':
                message += f"• Tax Rate: {withdrawal_settings.get('tax_percentage', 0)}%\n"
            elif withdrawal_settings.get('tax_type') == 'fixed':
                message += f"• Tax Amount: ₹{withdrawal_settings.get('tax_amount', 0)}\n"

            message += f"\n🔧 <b>Select an option to manage withdrawals:</b>"

            # Create withdrawal management keyboard
            keyboard = [
                [
                    InlineKeyboardButton(
                        '🔴 Disable Withdrawal' if withdrawal_enabled else '🟢 Enable Withdrawal',
                        callback_data='toggle_withdrawal_status'
                    )
                ],
                [
                    InlineKeyboardButton('📊 Set Percent Tax', callback_data='set_percent_tax'),
                    InlineKeyboardButton('💰 Set Fixed Tax', callback_data='set_fixed_tax')
                ],
                [
                    InlineKeyboardButton('⚠️ Disable One BANK/USDT One Time', callback_data='toggle_unique_accounts')
                ],
                [
                    InlineKeyboardButton('🚫 Ban/Unban Users Withdrawal', callback_data='ban_user_withdrawal'),
                    InlineKeyboardButton('✏️ Change Cashout Information', callback_data='change_user_cashout')
                ],
                [
                    InlineKeyboardButton('❌️ Reject All Withdrawals', callback_data='reject_all_withdrawals')
                ],
                [
                    InlineKeyboardButton('↩️ Go Back', callback_data='admin')
                ]
            ]

            await query.edit_message_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_manage_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_withdrawal_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle withdrawal enable/disable toggle"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get current withdrawal settings
            admin_settings = await self.admin_service.get_admin_settings()
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})
            current_status = withdrawal_settings.get('enabled', True)

            # Toggle status
            new_status = not current_status
            withdrawal_settings['enabled'] = new_status

            # Update withdrawal settings
            await self.admin_service.update_admin_setting('withdrawal_settings', withdrawal_settings)

            # Create response message
            if new_status:
                status_text = "🟢 Withdrawals have been ENABLED"
            else:
                status_text = "🔴 Withdrawals have been DISABLED"

            await query.answer(f"{status_text}", show_alert=True)

            # Refresh withdrawal management panel
            await self.handle_manage_withdrawals(update, context)

        except Exception as e:
            logger.error(f"Error in handle_toggle_withdrawal_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_percent_tax(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle setting percentage tax for withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "📊 <b>Set Percentage Tax</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Enter the percentage to deduct from withdrawals\n"
            message += "• Example: Enter '30' for 30% tax\n"
            message += "• Enter '0' to disable percentage tax\n\n"
            message += "📝 <b>Please send the percentage amount:</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_percent_tax')

        except Exception as e:
            logger.error(f"Error in handle_set_percent_tax: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_set_fixed_tax(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle setting fixed tax amount for withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "💰 <b>Set Fixed Tax Amount</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Enter the fixed amount to deduct from withdrawals\n"
            message += "• Example: Enter '5' for ₹5 tax per withdrawal\n"
            message += "• Enter '0' to disable fixed tax\n\n"
            message += "📝 <b>Please send the tax amount (₹):</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_fixed_tax')

        except Exception as e:
            logger.error(f"Error in handle_set_fixed_tax: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_unique_accounts(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle toggle for unique bank/USDT enforcement"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get current setting
            admin_settings = await self.admin_service.get_admin_settings()
            current_status = admin_settings.get('unique_bank_usdt_enforcement', False)

            # Toggle status
            new_status = not current_status
            await self.admin_service.update_admin_setting('unique_bank_usdt_enforcement', new_status)

            # Create response message
            if new_status:
                status_text = "🔒 Unique BANK/USDT enforcement ENABLED"
                detail_text = "Users cannot reuse bank/USDT details across accounts"
            else:
                status_text = "🔓 Unique BANK/USDT enforcement DISABLED"
                detail_text = "Users can reuse bank/USDT details"

            await query.answer(f"{status_text}", show_alert=True)

            # Refresh withdrawal management panel
            await self.handle_manage_withdrawals(update, context)

        except Exception as e:
            logger.error(f"Error in handle_toggle_unique_accounts: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_ban_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle ban/unban user withdrawal functionality"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "🚫 <b>Ban/Unban User Withdrawal</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Send user ID to ban/unban their withdrawal access\n"
            message += "• If user is currently banned, they will be unbanned\n"
            message += "• If user is not banned, they will be banned\n\n"
            message += "📝 <b>Please send the user ID:</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'ban_user_withdrawal')

        except Exception as e:
            logger.error(f"Error in handle_ban_user_withdrawal: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_change_user_cashout(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle changing user cashout information"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            message = "✏️ <b>Change User Cashout Information</b>\n\n"
            message += "💡 <b>Instructions:</b>\n"
            message += "• Send user ID to modify their bank/USDT details\n"
            message += "• This will reset their withdrawal method setup\n"
            message += "• User will need to re-setup their withdrawal method\n\n"
            message += "📝 <b>Please send the user ID:</b>"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Set session for next step
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'change_user_cashout')

        except Exception as e:
            logger.error(f"Error in handle_change_user_cashout: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_reject_all_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle rejecting all pending withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            # Get pending withdrawals count
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()
            pending_withdrawals = await withdrawal_service.get_pending_withdrawals()

            if not pending_withdrawals:
                await query.answer("ℹ️ No pending withdrawals to reject.", show_alert=True)
                return

            message = f"❌️ <b>Reject All Withdrawals</b>\n\n"
            message += f"⚠️ <b>Warning:</b> This action will reject ALL pending withdrawals!\n\n"
            message += f"📊 <b>Current Status:</b>\n"
            message += f"• Pending Withdrawals: {len(pending_withdrawals)}\n"
            message += f"• Total Amount: ₹{sum(w.get('amount', 0) for w in pending_withdrawals)}\n\n"
            message += f"🔴 <b>Are you sure you want to reject all pending withdrawals?</b>"

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Yes, Reject All', callback_data='confirm_reject_all_withdrawals'),
                    InlineKeyboardButton('❌ Cancel', callback_data='manage_withdrawals')
                ]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_reject_all_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_confirm_reject_all_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle confirmation of rejecting all pending withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            # Get all pending withdrawals
            pending_withdrawals = await withdrawal_service.get_pending_withdrawals()

            if not pending_withdrawals:
                await query.answer("ℹ️ No pending withdrawals found.", show_alert=True)
                await self.handle_manage_withdrawals(update, context)
                return

            rejected_count = 0
            total_amount = 0

            # Reject all pending withdrawals
            for withdrawal in pending_withdrawals:
                try:
                    withdrawal_id = withdrawal.get('_id')
                    amount = withdrawal.get('amount', 0)
                    user_id_withdrawal = withdrawal.get('user_id')

                    # Reject the withdrawal
                    success = await withdrawal_service.reject_withdrawal(withdrawal_id, settings.ADMIN_ID)

                    if success:
                        rejected_count += 1
                        total_amount += amount

                        # Send notification to user
                        try:
                            from telegram import Bot
                            bot = Bot(token=settings.BOT_TOKEN)
                            await bot.send_message(
                                chat_id=user_id_withdrawal,
                                text=f"❌ <b>Withdrawal Rejected</b>\n\n"
                                     f"Your withdrawal request of ₹{amount} has been rejected by admin.\n"
                                     f"The amount has been returned to your balance.",
                                parse_mode='HTML'
                            )
                        except Exception as notify_error:
                            logger.error(f"Error notifying user {user_id_withdrawal}: {notify_error}")

                except Exception as withdrawal_error:
                    logger.error(f"Error rejecting withdrawal {withdrawal.get('_id')}: {withdrawal_error}")

            # Create success message
            message = f"✅ <b>Bulk Rejection Complete</b>\n\n"
            message += f"📊 <b>Results:</b>\n"
            message += f"• Rejected Withdrawals: {rejected_count}\n"
            message += f"• Total Amount Returned: ₹{total_amount}\n"
            message += f"• Users Notified: {rejected_count}\n\n"
            message += f"💰 All amounts have been returned to user balances."

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manage_withdrawals')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            await query.answer(f"✅ Successfully rejected {rejected_count} withdrawals!", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_confirm_reject_all_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== ORIGINAL ADMIN FUNCTIONALITY WITH ENHANCEMENTS ====================
    # All original admin functions are preserved with improved functionality

    async def handle_admin_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle admin callback - redirect to main admin command"""
        await self.handle_admin_command(update, context)

    # ==================== ESSENTIAL ORIGINAL ADMIN HANDLERS ====================

    async def handle_maintenance_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle maintenance status toggle"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get current maintenance status
            admin_settings = await self.admin_service.get_admin_settings()
            current_status = admin_settings.get('maintenance_status', 'Off')

            # Toggle status
            new_status = 'On' if current_status == 'Off' else 'Off'

            # Update maintenance status
            await self.admin_service.update_admin_setting('maintenance_status', new_status)

            # Create response message
            status_emoji = "🔧" if new_status == 'On' else "✅"
            message = f"⚙️ <b>Maintenance Status Updated</b>\n\n"
            message += f"{status_emoji} <b>Status:</b> {new_status}\n\n"

            if new_status == 'On':
                message += "🔧 <b>Maintenance Mode Activated</b>\n"
                message += "• Bot is now in maintenance mode\n"
                message += "• Only admins can use the bot\n"
                message += "• Regular users will see maintenance message"
            else:
                message += "✅ <b>Normal Operation Restored</b>\n"
                message += "• Bot is now fully operational\n"
                message += "• All users can access the bot\n"
                message += "• All features are available"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_maintenance_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_gift_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast gift button callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for gift code input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_gift_code')

            message = "🎁 <b>Broadcast Gift Code</b>\n\n"
            message += "Please enter the gift code you want to broadcast to all users.\n\n"
            message += "💡 <b>Note:</b> This will send a gift code message to all bot users.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_gift_button: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_media(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast media callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            await query.answer()

            message = "🖼️ <b>Broadcast Media</b>\n\n"
            message += "Send a photo, video, or document to include in your broadcast.\n\n"
            message += "📝 <b>Supported formats:</b>\n"
            message += "• Photos (JPG, PNG)\n"
            message += "• Videos (MP4, MOV)\n"
            message += "• Documents (PDF, DOC, etc.)\n\n"
            message += "Send /cancel to cancel this process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set session for broadcast media input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_media')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_media: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast text callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            await query.answer()

            message = "🔤 <b>Broadcast Text</b>\n\n"
            message += "Enter the text message to send to all users.\n\n"
            message += "📝 <b>Formatting supported:</b>\n"
            message += "• <b>Bold text</b>\n"
            message += "• <i>Italic text</i>\n"
            message += "• <code>Code text</code>\n\n"
            message += "Send /cancel to cancel this process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set session for broadcast text input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_text')

            message = "📢 <b>Broadcast Message</b>\n\n"
            message += "Send the message you want to broadcast to all users.\n\n"
            message += "📝 <b>Supported Content:</b>\n"
            message += "• 📄 <b>Text</b> - Plain text with HTML formatting\n"
            message += "• 📸 <b>Photo</b> - Images with optional captions\n"
            message += "• 🎥 <b>Video</b> - Video files with optional captions\n"
            message += "• 📎 <b>Document</b> - Files with optional captions\n"
            message += "• 🎵 <b>Audio</b> - Audio files with optional captions\n\n"
            message += "💡 <b>Text Formatting:</b>\n"
            message += "• <b>Bold</b>, <i>Italic</i>, <code>Code</code>\n"
            message += "• Links and emojis are supported\n\n"
            message += "📤 Simply send your content (text, photo, video, etc.) and it will be broadcast to all users.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_text: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_broadcast_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast buttons callback"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            await query.answer()

            message = "⌨️ <b>Broadcast Buttons</b>\n\n"
            message += "Configure interactive buttons for your broadcast.\n\n"
            message += "📝 <b>Format:</b>\n"
            message += "<code>Button Text | URL</code>\n\n"
            message += "📝 <b>Example:</b>\n"
            message += "<code>Visit Website | https://example.com</code>\n"
            message += "<code>Join Channel | https://t.me/channel</code>\n\n"
            message += "💡 <b>Multiple buttons:</b> Send each button on a new line\n\n"
            message += "Send /cancel to cancel this process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set session for broadcast buttons input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'broadcast_buttons')

        except Exception as e:
            logger.error(f"Error in handle_broadcast_buttons: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_view_force_sub_channels(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view force subscription channels"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get force subscription channels
            admin_settings = await self.admin_service.get_admin_settings()
            force_channels = admin_settings.get('force_sub_channels', [])

            message = "📋 <b>Force Subscription Channels</b>\n\n"

            if force_channels:
                message += f"📊 <b>Total Channels:</b> {len(force_channels)}\n\n"
                for i, channel in enumerate(force_channels, 1):
                    channel_username = channel.get('username', 'Unknown')
                    channel_title = channel.get('title', 'Unknown')
                    message += f"{i}. <b>@{channel_username}</b>\n"
                    message += f"   📝 {channel_title}\n\n"
            else:
                message += "❌ <b>No force subscription channels configured.</b>\n\n"
                message += "💡 Add channels to require users to join before using the bot."

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('➕ Add Channel', callback_data='addForceSubChannel'),
                    InlineKeyboardButton('➖ Remove Channel', callback_data='removeForceSubChannel')
                ],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_view_force_sub_channels: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_otp_api_key(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle OTP API key setting"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for OTP API key input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_otp_api_key')

            message = "🔐 <b>Set OTP Website API Key</b>\n\n"
            message += "Please enter your OTP website API key.\n\n"
            message += "💡 <b>This key is used for:</b>\n"
            message += "• OTP verification services\n"
            message += "• Phone number validation\n"
            message += "• SMS verification\n\n"
            message += "🔒 <b>Security:</b> Your API key will be stored securely.\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_otp_api_key: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_per_refer_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle per refer amount setting"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for per refer amount input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_per_refer_amount')

            # Get current setting
            admin_settings = await self.admin_service.get_admin_settings()
            current_range = admin_settings.get('per_refer_amount_range', '1-3')

            message = "🧑‍🤝‍🧑 <b>Set Per Refer Amount</b>\n\n"
            message += f"💰 <b>Current Range:</b> ₹{current_range}\n\n"
            message += "Please enter the new referral amount range.\n\n"
            message += "📝 <b>Format:</b> min-max (e.g., 1-5)\n"
            message += "💡 <b>Example:</b> 2-4 (users get ₹2-4 per referral)\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_per_refer_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_joining_bonus_amount(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle joining bonus amount setting"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for joining bonus amount input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_joining_bonus_amount')

            # Get current setting
            admin_settings = await self.admin_service.get_admin_settings()
            current_range = admin_settings.get('joining_bonus_amount_range', '49-55')

            message = "💸 <b>Set Joining Bonus Amount</b>\n\n"
            message += f"💰 <b>Current Range:</b> ₹{current_range}\n\n"
            message += "Please enter the new joining bonus range.\n\n"
            message += "📝 <b>Format:</b> min-max (e.g., 50-60)\n"
            message += "💡 <b>Example:</b> 45-55 (new users get ₹45-55 bonus)\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_joining_bonus_amount: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== PLACEHOLDER HANDLERS FOR ORIGINAL BUTTONS ====================
    # These maintain the original button functionality with enhanced user feedback

    async def handle_add_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add balance functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for add balance
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_balance')

            message = "➕ <b>Add Balance to User</b>\n\n"
            message += "Please enter the user ID to add balance to.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_add_balance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_remove_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove balance functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for remove balance
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'remove_balance')

            message = "➖ <b>Remove Balance from User</b>\n\n"
            message += "Please enter the user ID to remove balance from.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_remove_balance: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle ban user functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for ban user
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'ban_user')

            message = "🚫 <b>Ban User</b>\n\n"
            message += "Please enter the user ID to ban.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "⚠️ <b>Warning:</b> Banned users cannot use the bot.\n\n"
            message += "Send /cancel to cancel this process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin_panel')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_ban_user: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_unban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle unban user functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for unban user
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'unban_user')

            message = "✔️ <b>Unban User</b>\n\n"
            message += "Please enter the user ID to unban.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "✅ <b>Note:</b> User will regain access to the bot.\n\n"
            message += "Send /cancel to cancel this process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin_panel')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_unban_user: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== ADDITIONAL ORIGINAL ADMIN HANDLERS ====================

    async def handle_check_user_record(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle check user record functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for user record check
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'check_user_record')

            message = "🔍 <b>Check User Record</b>\n\n"
            message += "Please enter the user ID to check.\n\n"
            message += "📝 <b>Format:</b> User ID (numbers only)\n"
            message += "💡 <b>Example:</b> *********\n\n"
            message += "📊 <b>Information shown:</b>\n"
            message += "• User details and statistics\n"
            message += "• Balance and transaction history\n"
            message += "• Referral information\n"
            message += "• Account status\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_check_user_record: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_manage_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manage withdrawals functionality"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Get pending withdrawals count for display
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()
            pending_withdrawals = await withdrawal_service.get_pending_withdrawals()
            pending_count = len(pending_withdrawals)
            total_pending_amount = sum(w['amount'] for w in pending_withdrawals)

            message = "🏧 <b>Withdrawal Management</b>\n\n"
            message += "💼 <b>Manage pending withdrawal requests</b>\n\n"
            message += f"📊 <b>Current Status:</b>\n"
            message += f"• Pending Requests: {pending_count}\n"
            message += f"• Total Pending Amount: ₹{total_pending_amount}\n\n"
            message += "📋 <b>Available Actions:</b>\n"
            message += "• View pending withdrawals\n"
            message += "• Approve withdrawal requests\n"
            message += "• Reject withdrawal requests\n"
            message += "• View withdrawal history\n\n"
            message += "💡 Select an action below:"

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Pass Withdrawal', callback_data='passUserWithdrawal'),
                    InlineKeyboardButton('❌ Fail Withdrawal', callback_data='failUserWithdrawal')
                ],
                [
                    InlineKeyboardButton('📊 View Pending', callback_data='viewPendingWithdrawals'),
                    InlineKeyboardButton('📋 Withdrawal History', callback_data='withdrawalHistory')
                ],
                [
                    InlineKeyboardButton('⚙️ Withdrawal Settings', callback_data='withdrawal_settings')
                ],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_manage_withdrawals: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== PLACEHOLDER HANDLERS FOR REMAINING BUTTONS ====================
    # These provide enhanced user feedback while maintaining original structure

    async def handle_main_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle main channel setting"""
        await self._handle_channel_setting(update, context, 'main_channel', 'Main Channel')

    async def handle_private_logs_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle private logs channel setting"""
        await self._handle_channel_setting(update, context, 'private_logs_channel', 'Private Logs Channel')

    async def _handle_channel_setting(self, update: Update, context: ContextTypes.DEFAULT_TYPE, setting_key: str, setting_name: str):
        """Generic handler for channel settings"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            # Set session for channel setting
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, f'set_{setting_key}')

            message = f"🏘️ <b>Set {setting_name}</b>\n\n"
            message += f"Please enter the {setting_name.lower()} username.\n\n"
            message += "📝 <b>Format:</b> @channelname or channelname\n"
            message += "💡 <b>Example:</b> @mychannel\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_channel_setting for {setting_name}: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # Enhanced placeholder handlers for remaining original buttons
    async def handle_add_force_sub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add force subscription channel"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_remove_force_sub_channel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove force subscription channel"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_pass_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle pass user withdrawal"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_fail_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle fail user withdrawal"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_manage_tasks(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manage tasks callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            all_tasks = await task_service.get_all_tasks()

            if not all_tasks:
                message = "📋 <b>Task Management</b>\n\n"
                message += "❌ No tasks found.\n\n"
                message += "Use \"➕ Add New Task\" to create your first task."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await self._safe_edit_message(query, message, keyboard, context)
                return

            message = "📋 <b>Task Management</b>\n\n"
            message += f"Total Tasks: {len(all_tasks)}\n\n"

            keyboard_buttons = []

            for task in all_tasks:
                status_icon = '✅' if task['status'] == 'active' else '❌'
                task_name = task['name'][:20] + '...' if len(task['name']) > 20 else task['name']

                keyboard_buttons.append([
                    InlineKeyboardButton(
                        f"{status_icon} {task_name} - ₹{task['reward_amount']}",
                        callback_data=f"editTask_{task['task_id']}"
                    )
                ])

            keyboard_buttons.extend([
                [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                [InlineKeyboardButton('📊 View Pending Submissions', callback_data='viewPendingSubmissions')],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            await self._safe_edit_message(query, message, keyboard, context)

        except Exception as e:
            logger.error(f"Error in handle_manage_tasks: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_add_new_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add new task callback (matching PHP version exactly)"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "➕ <b>Add New Task</b>\n\n"
            message += "Let's create a new task for users to complete.\n\n"
            message += "📝 <b>Step 1:</b> Enter the task name\n"
            message += "Keep it short and descriptive (max 50 characters).\n\n"
            message += "Send /cancel to cancel the process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'add_task_name')

        except Exception as e:
            logger.error(f"Error in handle_add_new_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task callback - show task details and editing options"""
        query = update.callback_query
        user_id = query.from_user.id
        chat_id = query.message.chat_id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)

            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            # Get task statistics
            total_submissions = await task_service.get_task_submission_count(task_id)
            pending_submissions = await task_service.get_pending_submissions_count(task_id)
            approved_submissions = await task_service.get_approved_submissions_count(task_id)

            status_icon = '✅' if task['status'] == 'active' else '❌'
            status_text = 'Active' if task['status'] == 'active' else 'Inactive'

            message = f"✏️ <b>Edit Task</b>\n\n"
            message += f"📝 <b>Name:</b> {task['name']}\n"
            message += f"📋 <b>Description:</b>\n{task['description']}\n\n"
            message += f"💰 <b>Reward:</b> ₹{task['reward_amount']}\n"
            message += f"⚙️ <b>Status:</b> {status_icon} {status_text}\n"
            message += f"🖼️ <b>Media:</b> {'Yes' if task.get('media_url') else 'No'}\n\n"
            message += f"📊 <b>Statistics:</b>\n"
            message += f"• Total Submissions: {total_submissions}\n"
            message += f"• Pending: {pending_submissions}\n"
            message += f"• Approved: {approved_submissions}\n\n"
            message += f"🆔 <b>Task ID:</b> <code>{task_id}</code>"

            keyboard_buttons = [
                [
                    InlineKeyboardButton('📝 Edit Name', callback_data=f'editTaskName_{task_id}'),
                    InlineKeyboardButton('📋 Edit Description', callback_data=f'editTaskDesc_{task_id}')
                ],
                [
                    InlineKeyboardButton('💰 Edit Reward', callback_data=f'editTaskReward_{task_id}'),
                    InlineKeyboardButton('🖼️ Edit Media', callback_data=f'editTaskMedia_{task_id}')
                ],
                [
                    InlineKeyboardButton(
                        f'⚙️ {"Deactivate" if task["status"] == "active" else "Activate"}',
                        callback_data=f'toggleTaskStatus_{task_id}'
                    )
                ],
                [
                    InlineKeyboardButton('🗑️ Delete Task', callback_data=f'deleteTask_{task_id}')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Tasks', callback_data='manageTasks')
                ]
            ]

            keyboard = InlineKeyboardMarkup(keyboard_buttons)

            # Handle media if task has media_url - use two-message approach
            if task.get('media_url') and task['media_url'].strip():
                try:
                    # Delete the current message first
                    await query.message.delete()

                    # Message 1: Send the task image alone (with minimal caption)
                    await context.bot.send_photo(
                        chat_id=chat_id,
                        photo=task['media_url'],
                        caption=f"🖼️ <b>Task Image:</b> {task['name']}"
                    )

                    # Message 2: Send the task details as a separate text message with buttons
                    await context.bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        parse_mode='HTML',
                        reply_markup=keyboard
                    )
                except Exception as e:
                    logger.error(f"Error sending task media with two-message approach: {e}")
                    # Fallback to text message using safe edit
                    await self._safe_edit_message(query, message, keyboard, context)
            else:
                await self._safe_edit_message(query, message, keyboard, context)

        except Exception as e:
            logger.error(f"Error in handle_edit_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_name(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task name"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Name</b>\n\n"
            message += "Enter the new task name (max 50 characters):\n\n"
            message += "Send /cancel to cancel this process."

            await self._safe_edit_message(query, message, None, context)

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_name', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_name: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_description(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task description"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Description</b>\n\n"
            message += "Enter the new task description:\n\n"
            message += "Provide detailed instructions on what users need to do.\n\n"
            message += "Send /cancel to cancel this process."

            await self._safe_edit_message(query, message, None, context)

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_description', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_description: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_reward(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task reward"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Reward</b>\n\n"
            message += "Enter the new reward amount (numbers only):\n\n"
            message += "💡 <b>Example:</b> 50 (for ₹50)\n\n"
            message += "Send /cancel to cancel this process."

            await query.edit_message_text(message, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_reward', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_reward: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_edit_task_media(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle edit task media"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            message = "✏️ <b>Edit Task Media</b>\n\n"
            message += "Send a photo/image for the task or send 'none' to remove media:\n\n"
            message += "📸 The image will be shown to users when they view the task.\n\n"
            message += "Send /cancel to cancel this process."

            await self._safe_edit_message(query, message, None, context)

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'edit_task_media', {'task_id': task_id})

        except Exception as e:
            logger.error(f"Error in handle_edit_task_media: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_toggle_task_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle toggle task status"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            new_status = 'inactive' if task['status'] == 'active' else 'active'

            if await task_service.update_task(task_id, {'status': new_status}):
                status_text = 'activated' if new_status == 'active' else 'deactivated'
                await query.answer(f"✅ Task {status_text} successfully!", show_alert=True)

                # Refresh the edit task view
                await self.handle_edit_task(update, context, task_id)
            else:
                await query.answer("❌ Failed to update task status.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_toggle_task_status: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_view_pending_submissions(self, update: Update, context: ContextTypes.DEFAULT_TYPE, page: int = 1) -> None:
        """View pending task submissions with pagination"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get pending task submissions
            from services.task_service import TaskService
            from services.user_service import UserService
            task_service = TaskService()
            user_service = UserService()

            pending_submissions = await task_service.get_pending_task_submissions()

            # Get detailed information for each submission
            detailed_submissions = []
            for submission in pending_submissions:
                # Get task details
                task = await task_service.get_task_by_id(submission['task_id'])
                # Get user details
                user = await user_service.get_user(submission['user_id'])

                if task and user:
                    submission_info = {
                        'submission_id': submission['submission_id'],
                        'user_name': user.get('first_name', 'Unknown'),
                        'user_id': submission['user_id'],
                        'task_name': task['name'],
                        'task_reward': task['reward_amount'],
                        'submitted_at': submission['submitted_at'],
                        'file_id': submission['file_id']
                    }
                    detailed_submissions.append(submission_info)

            # Pagination settings
            per_page = 12
            total_submissions = len(detailed_submissions)
            total_pages = max(1, (total_submissions + per_page - 1) // per_page)
            page = max(1, min(page, total_pages))

            start_idx = (page - 1) * per_page
            end_idx = start_idx + per_page
            page_submissions = detailed_submissions[start_idx:end_idx]

            # Build message
            message = f"⏳ <b>Pending Task Submissions</b>\n\n"
            message += f"📊 <b>Total Pending:</b> {total_submissions}\n\n"

            if not page_submissions:
                message += "✅ No pending submissions to review."
            else:
                message += f"<b>Page {page} of {total_pages}</b>\n\n"

                for i, submission in enumerate(page_submissions, start_idx + 1):
                    user_name = submission['user_name']
                    user_id = submission['user_id']
                    task_name = submission['task_name']
                    reward = submission['task_reward']

                    # Format date
                    from datetime import datetime
                    try:
                        date_str = datetime.fromtimestamp(submission['submitted_at']).strftime("%Y-%m-%d %H:%M")
                    except:
                        date_str = "Unknown"

                    message += f"{i:02d}. <b>{user_name}</b> (ID: {user_id})\n"
                    message += f"    Task: {task_name}\n"
                    message += f"    Reward: ₹{reward} | Date: {date_str}\n"
                    message += f"    📋 <code>/review_{submission['submission_id']}</code>\n\n"

            # Build keyboard
            keyboard = []

            # Navigation buttons
            nav_buttons = []
            if page > 1:
                nav_buttons.append(InlineKeyboardButton('⬅️ Previous', callback_data=f'pending_submissions_{page-1}'))
            if page < total_pages:
                nav_buttons.append(InlineKeyboardButton('➡️ Next', callback_data=f'pending_submissions_{page+1}'))

            if nav_buttons:
                keyboard.append(nav_buttons)

            # Action buttons
            if page_submissions:
                keyboard.append([
                    InlineKeyboardButton('🔄 Refresh', callback_data='viewPendingSubmissions'),
                    InlineKeyboardButton('📋 Review First', callback_data=f'review_submission_{page_submissions[0]["submission_id"]}')
                ])

            keyboard.append([InlineKeyboardButton('↩️ Back to Task Management', callback_data='manageTasks')])

            await self._safe_edit_message(query, message, InlineKeyboardMarkup(keyboard), context)

        except Exception as e:
            logger.error(f"Error in handle_view_pending_submissions: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_review_submission(self, update: Update, context: ContextTypes.DEFAULT_TYPE, submission_id: str) -> None:
        """Review a specific task submission"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get submission details
            from services.task_service import TaskService
            from services.user_service import UserService
            task_service = TaskService()
            user_service = UserService()

            submission = await task_service.get_submission_by_id(submission_id)
            if not submission:
                await query.answer("❌ Submission not found.", show_alert=True)
                return

            # Get task and user details
            task = await task_service.get_task_by_id(submission['task_id'])
            user = await user_service.get_user(submission['user_id'])

            if not task or not user:
                await query.answer("❌ Task or user not found.", show_alert=True)
                return

            # Format submission date
            from datetime import datetime
            try:
                date_str = datetime.fromtimestamp(submission['submitted_at']).strftime("%Y-%m-%d %H:%M")
            except:
                date_str = "Unknown"

            # Build message
            message = f"📋 <b>Task Submission Review</b>\n\n"
            message += f"👤 <b>User:</b> {user['first_name']} (ID: {submission['user_id']})\n"
            message += f"📋 <b>Task:</b> {task['name']}\n"
            message += f"💰 <b>Reward:</b> ₹{task['reward_amount']}\n"
            message += f"📅 <b>Submitted:</b> {date_str}\n"
            message += f"🆔 <b>Submission ID:</b> {submission_id}\n\n"
            message += f"📸 <b>Submitted File:</b> See attachment below\n\n"
            message += f"⚡ <b>Actions:</b> Choose to approve or reject this submission."

            # Build keyboard
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Approve', callback_data=f'approveTask_{submission_id}'),
                    InlineKeyboardButton('❌ Reject', callback_data=f'rejectTask_{submission_id}')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Pending', callback_data='viewPendingSubmissions')
                ]
            ])

            # Use two-message approach: First delete current message, then send file and review message separately
            try:
                # Delete the current message first
                await query.message.delete()

                # Message 1: Send the submitted file
                try:
                    await context.bot.send_photo(
                        chat_id=query.message.chat_id,
                        photo=submission['file_id'],
                        caption=f"📸 <b>Submission from {user['first_name']}</b>\n🆔 Submission ID: {submission_id}"
                    )
                except:
                    try:
                        await context.bot.send_document(
                            chat_id=query.message.chat_id,
                            document=submission['file_id'],
                            caption=f"📄 <b>Submission from {user['first_name']}</b>\n🆔 Submission ID: {submission_id}"
                        )
                    except:
                        message += f"\n\n⚠️ <b>Note:</b> Could not display submitted file."

                # Message 2: Send the review message as a separate text message with buttons
                await context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text=message,
                    parse_mode='HTML',
                    reply_markup=keyboard
                )

            except Exception as send_error:
                logger.error(f"Error in two-message approach for review submission: {send_error}")
                # Fallback to safe edit if two-message approach fails
                await self._safe_edit_message(query, message, keyboard, context)

        except Exception as e:
            logger.error(f"Error in handle_review_submission: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_approve_task_submission(self, update: Update, context: ContextTypes.DEFAULT_TYPE, submission_id: str) -> None:
        """Approve a task submission"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Approve the submission
            from services.task_service import TaskService
            task_service = TaskService()

            success = await task_service.approve_task_submission(submission_id, "Approved by admin")

            if success:
                # Show success message with navigation back to pending submissions
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Pending Submissions', callback_data='viewPendingSubmissions')]
                ])

                message = (f"✅ <b>Task Submission Approved</b>\n\n"
                          f"Submission ID: {submission_id}\n"
                          f"User has been rewarded and notified.\n\n"
                          f"Click below to return to pending submissions.")

                # Use smart navigation to handle media-to-text transitions
                from handlers.callback_handlers import CallbackHandlers
                callback_handlers = CallbackHandlers()
                await callback_handlers._smart_navigate_to_text_message(query, message, keyboard, context)
            else:
                # Show failure message with navigation back to pending submissions
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Pending Submissions', callback_data='viewPendingSubmissions')]
                ])

                message = (f"❌ <b>Failed to Approve Submission</b>\n\n"
                          f"Submission ID: {submission_id}\n"
                          f"Please try again later.")

                # Use smart navigation to handle media-to-text transitions
                from handlers.callback_handlers import CallbackHandlers
                callback_handlers = CallbackHandlers()
                await callback_handlers._smart_navigate_to_text_message(query, message, keyboard, context)

        except Exception as e:
            logger.error(f"Error in handle_approve_task_submission: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_reject_task_submission(self, update: Update, context: ContextTypes.DEFAULT_TYPE, submission_id: str) -> None:
        """Reject a task submission with reason"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = f"❌ <b>Reject Task Submission</b>\n\n"
            message += f"Submission ID: {submission_id}\n\n"
            message += f"Enter rejection reason (optional):\n"
            message += f"Send 'skip' to reject without reason or /cancel to cancel."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Pending Submissions', callback_data='viewPendingSubmissions')]
            ])

            await self._safe_edit_message(query, message, keyboard, context)

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'reject_task_submission', {'submission_id': submission_id})

        except Exception as e:
            logger.error(f"Error in handle_reject_task_submission: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_task_confirm(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle delete task confirmation"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            total_submissions = await task_service.get_task_submission_count(task_id)

            message = f"🗑️ <b>Delete Task</b>\n\n"
            message += f"⚠️ <b>Warning:</b> This action cannot be undone!\n\n"
            message += f"📝 <b>Task:</b> {task['name']}\n"
            message += f"💰 <b>Reward:</b> ₹{task['reward_amount']}\n"
            message += f"📊 <b>Submissions:</b> {total_submissions}\n\n"
            message += f"Are you sure you want to delete this task?\n"
            message += f"All submissions will also be deleted."

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Yes, Delete', callback_data=f'confirmDeleteTask_{task_id}'),
                    InlineKeyboardButton('❌ Cancel', callback_data=f'editTask_{task_id}')
                ]
            ])

            # Use smart navigation to handle media-to-text transitions
            from handlers.callback_handlers import CallbackHandlers
            callback_handlers = CallbackHandlers()
            await callback_handlers._smart_navigate_to_text_message(query, message, keyboard, context)

        except Exception as e:
            logger.error(f"Error in handle_delete_task_confirm: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_delete_task(self, update: Update, context: ContextTypes.DEFAULT_TYPE, task_id: str):
        """Handle delete task"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task:
                await query.answer("❌ Task not found.", show_alert=True)
                return

            task_name = task['name']

            if await task_service.delete_task(task_id):
                message = f"✅ <b>Task Deleted Successfully</b>\n\n"
                message += f"📝 <b>Deleted Task:</b> {task_name}\n\n"
                message += f"The task and all its submissions have been removed."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks')],
                    [InlineKeyboardButton('➕ Add New Task', callback_data='addNewTask')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                # Use smart navigation to handle media-to-text transitions
                from handlers.callback_handlers import CallbackHandlers
                callback_handlers = CallbackHandlers()
                await callback_handlers._smart_navigate_to_text_message(query, message, keyboard, context)
                await query.answer("✅ Task deleted successfully!", show_alert=True)
            else:
                await query.answer("❌ Failed to delete task. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_delete_task: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_generate_gift_code(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle generate gift code"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_configure_level_rewards(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle configure level rewards - functional implementation"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current level rewards configuration using the dedicated service
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()

            level_rewards_config = await level_rewards_service.get_level_rewards_config()
            level_rewards_enabled = await level_rewards_service.is_level_rewards_enabled()

            # Build current configuration display
            message = "🏆 <b>Level Rewards Configuration</b>\n\n"
            message += f"Status: {'🟢 Enabled' if level_rewards_enabled else '🔴 Disabled'}\n\n"
            message += "<b>Current Configuration:</b>\n"

            referral_requirements = level_rewards_config.get('referral_requirements', [])
            bonus_amounts = level_rewards_config.get('bonus_amounts', [])

            for i, (req, bonus) in enumerate(zip(referral_requirements, bonus_amounts), 1):
                message += f"Level {i}: {req} referrals = ₹{bonus}\n"

            message += "\nSend referral requirements (comma-separated):\n"
            message += "Example: 1,5,10,15,20,25\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'configure_level_rewards')

        except Exception as e:
            logger.error(f"Error in handle_configure_level_rewards: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)



    async def handle_custom_referral_links(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle custom referral links"""
        query = update.callback_query
        await query.answer("🚧 Enhanced feature coming soon! Original functionality preserved.", show_alert=True)

    async def handle_withdrawal_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdrawal settings - comprehensive configuration interface"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current withdrawal settings
            withdrawal_settings = await self.admin_service.get_withdrawal_settings()

            # Build settings overview message
            message = "⚙️ <b>Withdrawal Settings</b>\n\n"
            message += "📊 <b>Current Configuration:</b>\n"

            # Withdrawal status
            enabled = withdrawal_settings.get('enabled', True)
            message += f"• Status: {'🟢 Enabled' if enabled else '🔴 Disabled'}\n"

            # Tax configuration
            tax_type = withdrawal_settings.get('tax_type', 'none')
            tax_amount = withdrawal_settings.get('tax_amount', 0)
            tax_percentage = withdrawal_settings.get('tax_percentage', 0)

            if tax_type == 'none':
                message += "• Tax: 🚫 No Tax\n"
            elif tax_type == 'fixed':
                message += f"• Tax: 💰 Fixed ₹{tax_amount}\n"
            elif tax_type == 'percentage':
                message += f"• Tax: 📊 {tax_percentage}% of amount\n"

            message += "\n💡 <b>Configuration Options:</b>"

            # Build keyboard with current settings
            keyboard = [
                # Withdrawal Status Toggle
                [
                    InlineKeyboardButton(
                        f"{'🔴 Disable' if enabled else '🟢 Enable'} Withdrawals",
                        callback_data='toggle_withdrawal_status'
                    )
                ],
                # Tax Configuration
                [
                    InlineKeyboardButton('💰 Tax Configuration', callback_data='withdrawal_tax_config')
                ],
                # Navigation
                [
                    InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')
                ]
            ]

            await query.edit_message_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_withdrawal_settings: {e}")
            await query.answer("❌ Error loading withdrawal settings.", show_alert=True)

    async def handle_withdrawal_tax_config(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdrawal tax configuration"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current tax settings
            withdrawal_settings = await self.admin_service.get_withdrawal_settings()
            tax_type = withdrawal_settings.get('tax_type', 'none')
            tax_amount = withdrawal_settings.get('tax_amount', 0)
            tax_percentage = withdrawal_settings.get('tax_percentage', 0)

            # Build tax configuration message
            message = "💰 <b>Tax Configuration</b>\n\n"
            message += "📊 <b>Current Tax Settings:</b>\n"

            if tax_type == 'none':
                message += "• Type: 🚫 No Tax Applied\n"
            elif tax_type == 'fixed':
                message += f"• Type: 💰 Fixed Amount\n"
                message += f"• Amount: ₹{tax_amount} per withdrawal\n"
            elif tax_type == 'percentage':
                message += f"• Type: 📊 Percentage Based\n"
                message += f"• Rate: {tax_percentage}% of withdrawal amount\n"

            message += "\n💡 <b>Tax Configuration Options:</b>\n"
            message += "Choose how withdrawal tax should be calculated:"

            # Build keyboard with tax options
            keyboard = [
                # Tax Type Options
                [
                    InlineKeyboardButton(
                        f"🚫 No Tax {'✅' if tax_type == 'none' else ''}",
                        callback_data='set_tax_none'
                    )
                ],
                [
                    InlineKeyboardButton(
                        f"💰 Fixed Amount {'✅' if tax_type == 'fixed' else ''}",
                        callback_data='set_tax_fixed'
                    )
                ],
                [
                    InlineKeyboardButton(
                        f"📊 Percentage {'✅' if tax_type == 'percentage' else ''}",
                        callback_data='set_tax_percentage'
                    )
                ],
                # Navigation
                [
                    InlineKeyboardButton('↩️ Back to Withdrawal Settings', callback_data='withdrawal_settings')
                ]
            ]

            await query.edit_message_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_withdrawal_tax_config: {e}")
            await query.answer("❌ Error loading tax configuration.", show_alert=True)

    async def handle_set_tax_none(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle setting no tax"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Update tax settings to none
            withdrawal_settings = await self.admin_service.get_withdrawal_settings()
            withdrawal_settings.update({
                'tax_type': 'none',
                'tax_amount': 0,
                'tax_percentage': 0
            })

            success = await self.admin_service.update_withdrawal_settings(withdrawal_settings)

            if success:
                message = "✅ <b>Tax Configuration Updated</b>\n\n"
                message += "🚫 <b>No Tax Applied</b>\n\n"
                message += "Users will receive the full withdrawal amount without any tax deductions."
            else:
                message = "❌ <b>Failed to Update Tax Configuration</b>\n\n"
                message += "Please try again later."

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Tax Configuration', callback_data='withdrawal_tax_config')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_set_tax_none: {e}")
            await query.answer("❌ Error updating tax settings.", show_alert=True)

    async def handle_set_tax_fixed(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle setting fixed tax amount"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "💰 <b>Set Fixed Tax Amount</b>\n\n"
            message += "Enter the fixed tax amount to be deducted from each withdrawal:\n\n"
            message += "Example: 10 (for ₹10 tax per withdrawal)\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Tax Configuration', callback_data='withdrawal_tax_config')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_fixed_tax')

        except Exception as e:
            logger.error(f"Error in handle_set_tax_fixed: {e}")
            await query.answer("❌ Error setting up fixed tax configuration.", show_alert=True)

    async def handle_set_tax_percentage(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle setting percentage tax"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            message = "📊 <b>Set Percentage Tax</b>\n\n"
            message += "Enter the tax percentage to be deducted from each withdrawal:\n\n"
            message += "Example: 5 (for 5% tax on withdrawal amount)\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Tax Configuration', callback_data='withdrawal_tax_config')]
            ])

            await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')

            # Set user session
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()
            await session_handlers.set_user_session(user_id, 'set_percent_tax')

        except Exception as e:
            logger.error(f"Error in handle_set_tax_percentage: {e}")
            await query.answer("❌ Error setting up percentage tax configuration.", show_alert=True)

    async def handle_toggle_withdrawal_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle toggling withdrawal status"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current withdrawal settings
            withdrawal_settings = await self.admin_service.get_withdrawal_settings()
            current_status = withdrawal_settings.get('enabled', True)

            # Toggle status
            new_status = not current_status
            withdrawal_settings['enabled'] = new_status

            # Update settings
            success = await self.admin_service.update_withdrawal_settings(withdrawal_settings)

            if success:
                if new_status:
                    message = "✅ <b>Withdrawals Enabled</b>\n\n"
                    message += "🟢 Users can now submit withdrawal requests.\n\n"
                    message += "All withdrawal functionality is active."
                    status_icon = "🟢"
                else:
                    message = "✅ <b>Withdrawals Disabled</b>\n\n"
                    message += "🔴 Users cannot submit new withdrawal requests.\n\n"
                    message += "Existing pending withdrawals can still be processed."
                    status_icon = "🔴"
            else:
                message = "❌ <b>Failed to Update Withdrawal Status</b>\n\n"
                message += "Please try again later."

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Withdrawal Settings', callback_data='withdrawal_settings')]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_toggle_withdrawal_status: {e}")
            await query.answer("❌ Error toggling withdrawal status.", show_alert=True)

    async def handle_pass_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle pass user withdrawal - show list of pending withdrawals for approval"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Answer callback query immediately to prevent timeout
            await query.answer("Loading withdrawal requests...")

            from services.withdrawal_service import WithdrawalService
            import time

            start_time = time.time()
            withdrawal_service = WithdrawalService()

            # Get all pending withdrawals with timeout handling
            try:
                pending_withdrawals = await withdrawal_service.get_pending_withdrawals()
                load_time = time.time() - start_time
                logger.info(f"Loaded {len(pending_withdrawals)} pending withdrawals for approval in {load_time:.3f}s")
            except Exception as e:
                logger.error(f"Error getting pending withdrawals for approval: {e}")
                await query.edit_message_text(
                    "❌ <b>Database Error</b>\n\n"
                    "Unable to load pending withdrawals due to database timeout. Please try again.",
                    parse_mode='HTML',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('🔄 Retry', callback_data='passUserWithdrawal')],
                        [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')]
                    ])
                )
                return

            if not pending_withdrawals:
                await query.edit_message_text(
                    "ℹ️ <b>No Pending Withdrawals</b>\n\n"
                    "There are currently no withdrawal requests pending approval.",
                    parse_mode='HTML',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')]
                    ])
                )
                return

            # Create message with pending withdrawals
            message = "✅ <b>Approve Withdrawal Requests</b>\n\n"
            message += f"📊 <b>Total Pending:</b> {len(pending_withdrawals)} requests\n\n"

            # Helper function to sanitize text for buttons
            def sanitize_button_text(text):
                """Sanitize text for button display"""
                if not text:
                    return ""
                # Remove problematic characters that could cause parsing issues
                return str(text).replace('<', '').replace('>', '').replace('&', '').replace('"', '').replace("'", '')

            keyboard_buttons = []

            for withdrawal in pending_withdrawals[:10]:  # Show first 10
                # Sanitize user data for button text
                first_name = sanitize_button_text(withdrawal['first_name'])
                username = sanitize_button_text(withdrawal['username'])

                user_display = first_name
                if username:
                    user_display += f" (@{username})"

                # Truncate long names to prevent button text overflow
                if len(user_display) > 25:
                    user_display = user_display[:22] + "..."

                button_text = f"✅ {user_display} - ₹{withdrawal['amount']:,}"
                callback_data = f"approve_withdrawal_{withdrawal['user_id']}"

                keyboard_buttons.append([
                    InlineKeyboardButton(button_text, callback_data=callback_data)
                ])

            if len(pending_withdrawals) > 10:
                message += f"\n<i>Showing first 10 of {len(pending_withdrawals)} pending requests</i>"

            keyboard_buttons.append([
                InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')
            ])

            await query.edit_message_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard_buttons),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_pass_user_withdrawal: {e}")
            await query.answer("❌ Error loading pending withdrawals.", show_alert=True)

    async def handle_fail_user_withdrawal(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle fail user withdrawal - show list of pending withdrawals for rejection"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            # Get all pending withdrawals
            pending_withdrawals = await withdrawal_service.get_pending_withdrawals()

            if not pending_withdrawals:
                await query.edit_message_text(
                    "ℹ️ <b>No Pending Withdrawals</b>\n\n"
                    "There are currently no withdrawal requests pending rejection.",
                    parse_mode='HTML',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')]
                    ])
                )
                return

            # Create message with pending withdrawals
            message = "❌ <b>Reject Withdrawal Requests</b>\n\n"
            message += f"📊 <b>Total Pending:</b> {len(pending_withdrawals)} requests\n\n"

            # Helper function to sanitize text for buttons
            def sanitize_button_text(text):
                """Sanitize text for button display"""
                if not text:
                    return ""
                return str(text).replace('<', '').replace('>', '').replace('&', '').replace('"', '').replace("'", '')

            keyboard_buttons = []

            for withdrawal in pending_withdrawals[:10]:  # Show first 10
                # Sanitize user data for button text
                first_name = sanitize_button_text(withdrawal['first_name'])
                username = sanitize_button_text(withdrawal['username'])

                user_display = first_name
                if username:
                    user_display += f" (@{username})"

                # Truncate long names to prevent button text overflow
                if len(user_display) > 25:
                    user_display = user_display[:22] + "..."

                button_text = f"❌ {user_display} - ₹{withdrawal['amount']:,}"
                callback_data = f"reject_withdrawal_{withdrawal['user_id']}"

                keyboard_buttons.append([
                    InlineKeyboardButton(button_text, callback_data=callback_data)
                ])

            if len(pending_withdrawals) > 10:
                message += f"\n<i>Showing first 10 of {len(pending_withdrawals)} pending requests</i>"

            keyboard_buttons.append([
                InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')
            ])

            await query.edit_message_text(
                message,
                reply_markup=InlineKeyboardMarkup(keyboard_buttons),
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_fail_user_withdrawal: {e}")
            await query.answer("❌ Error loading pending withdrawals.", show_alert=True)

    async def handle_view_pending_withdrawals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle view pending withdrawals - display detailed list of all pending withdrawals"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Answer callback query immediately to prevent timeout
            await query.answer("Loading pending withdrawals...")

            from services.withdrawal_service import WithdrawalService
            import time

            start_time = time.time()
            withdrawal_service = WithdrawalService()

            # Get all pending withdrawals with timeout handling
            try:
                pending_withdrawals = await withdrawal_service.get_pending_withdrawals()
                load_time = time.time() - start_time
                logger.info(f"Loaded {len(pending_withdrawals)} pending withdrawals in {load_time:.3f}s")
            except Exception as e:
                logger.error(f"Error loading pending withdrawals: {e}")
                await query.edit_message_text(
                    "❌ <b>Database Error</b>\n\n"
                    "Unable to load pending withdrawals due to database timeout. Please try again.",
                    parse_mode='HTML',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('🔄 Retry', callback_data='viewPendingWithdrawals')],
                        [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')]
                    ])
                )
                return

            if not pending_withdrawals:
                await query.edit_message_text(
                    "ℹ️ <b>No Pending Withdrawals</b>\n\n"
                    "There are currently no withdrawal requests pending review.",
                    parse_mode='HTML',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')]
                    ])
                )
                return

            # Helper function to sanitize text for HTML
            def sanitize_html(text):
                """Sanitize text to prevent HTML parsing errors"""
                if not text:
                    return ""
                return str(text).replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;').replace('"', '&quot;')

            # Create detailed message with pending withdrawals
            message = "📊 <b>Pending Withdrawal Requests</b>\n\n"
            message += f"📈 <b>Total Requests:</b> {len(pending_withdrawals)}\n"

            total_amount = sum(w['amount'] for w in pending_withdrawals)
            message += f"💰 <b>Total Amount:</b> ₹{total_amount:,}\n\n"

            # Show detailed list (limit to 10 for better performance)
            display_limit = min(10, len(pending_withdrawals))
            for i, withdrawal in enumerate(pending_withdrawals[:display_limit], 1):
                # Sanitize user data
                first_name = sanitize_html(withdrawal['first_name'])
                username = sanitize_html(withdrawal['username'])

                user_display = first_name
                if username:
                    user_display += f" (@{username})"

                method_icon = "🏦" if withdrawal['withdrawal_method'] == 'bank' else "₿"

                message += f"{i}. {method_icon} <b>{user_display}</b>\n"
                message += f"   💰 Amount: ₹{withdrawal['amount']:,}\n"
                message += f"   📱 User ID: <code>{withdrawal['user_id']}</code>\n"

                # Add account info preview with sanitization
                account_info = withdrawal.get('account_info', {})
                if withdrawal['withdrawal_method'] == 'bank':
                    account_number = account_info.get('account_number', '')
                    if account_number:
                        message += f"   🏦 Account: <code>{account_number}</code>\n"
                else:
                    usdt_address = account_info.get('usdt_address', '')
                    if usdt_address:
                        message += f"   ₿ USDT: <code>{usdt_address}</code>\n"

                message += "\n"

            if len(pending_withdrawals) > display_limit:
                message += f"<i>... and {len(pending_withdrawals) - display_limit} more requests</i>\n\n"

            message += "💡 <b>Quick Actions:</b>"

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Approve Requests', callback_data='passUserWithdrawal'),
                    InlineKeyboardButton('❌ Reject Requests', callback_data='failUserWithdrawal')
                ],
                [
                    InlineKeyboardButton('🔄 Refresh List', callback_data='viewPendingWithdrawals')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')
                ]
            ])

            # Send message with timeout handling
            try:
                await query.edit_message_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                total_time = time.time() - start_time
                logger.info(f"handle_view_pending_withdrawals completed in {total_time:.3f}s")

            except Exception as e:
                logger.error(f"Error sending pending withdrawals message: {e}")
                # Fallback message if HTML parsing fails
                fallback_message = (
                    f"📊 Pending Withdrawal Requests\n\n"
                    f"📈 Total Requests: {len(pending_withdrawals)}\n"
                    f"💰 Total Amount: ₹{total_amount:,}\n\n"
                    f"⚠️ Display error occurred. Use Quick Actions below."
                )

                try:
                    await query.edit_message_text(
                        fallback_message,
                        reply_markup=keyboard
                    )
                except Exception as fallback_error:
                    logger.error(f"Fallback message also failed: {fallback_error}")
                    await query.answer("❌ Error displaying withdrawals. Please try again.", show_alert=True)

        except Exception as e:
            logger.error(f"Error in handle_view_pending_withdrawals: {e}")
            await query.answer("❌ Error loading pending withdrawals.", show_alert=True)

    async def handle_withdrawal_approval_confirmation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int):
        """Handle withdrawal approval confirmation dialog"""
        query = update.callback_query
        admin_id = query.from_user.id

        try:
            if not await is_admin_async(admin_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            await query.answer("Loading withdrawal details...")

            from services.user_service import UserService
            from services.admin_service import AdminService
            import time
            from datetime import datetime

            user_service = UserService()
            admin_service = AdminService()

            # Get user data
            user = await user_service.get_user(target_user_id)
            if not user:
                await query.answer("❌ User not found.", show_alert=True)
                return

            # Check if user has pending withdrawal
            pending_amount = user.get('withdraw_under_review', 0)
            if pending_amount <= 0:
                await query.answer("❌ No pending withdrawal found for this user.", show_alert=True)
                return

            # Get account information
            account_info = user.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'bank')

            # Calculate tax if applicable
            tax_calculation = await admin_service.calculate_withdrawal_tax(pending_amount)



            # Build confirmation message
            message = "🔍 <b>Withdrawal Approval Confirmation</b>\n\n"

            # User Information
            message += "👤 <b>User Information:</b>\n"
            message += f"▫️ <b>Name:</b> {user.get('first_name', 'Unknown')}\n"
            if user.get('username'):
                message += f"▫️ <b>Username:</b> @{user.get('username')}\n"
            message += f"▫️ <b>User ID:</b> <code>{target_user_id}</code>\n"
            message += f"▫️ <b>Current Balance:</b> ₹{user.get('balance', 0):,}\n\n"

            # Withdrawal Details
            message += "💰 <b>Withdrawal Details:</b>\n"
            message += f"▫️ <b>Requested Amount:</b> ₹{pending_amount:,}\n"
            message += f"▫️ <b>Tax Deducted:</b> ₹{tax_calculation.get('tax_deducted', 0):,.2f}\n"
            message += f"▫️ <b>Final Amount:</b> ₹{tax_calculation.get('final_amount', pending_amount):,.2f}\n"
            message += f"▫️ <b>Method:</b> {withdrawal_method.upper()}\n\n"

            # Account Information based on method
            if withdrawal_method == 'bank':
                message += "🏦 <b>Bank Account Details:</b>\n"
                message += f"▫️ <b>Account Holder:</b> {account_info.get('name', 'Not provided')}\n"

                account_number = account_info.get('account_number', '')
                if account_number:
                    message += f"▫️ <b>Account Number:</b> <code>{account_number}</code>\n"
                else:
                    message += f"▫️ <b>Account Number:</b> <i>Not provided</i>\n"

                message += f"▫️ <b>IFSC Code:</b> {account_info.get('ifsc', 'Not provided')}\n"
                message += f"▫️ <b>Mobile:</b> {account_info.get('mobile_number', 'Not provided')}\n"
                message += f"▫️ <b>Email:</b> {account_info.get('email', 'Not provided')}\n"

            elif withdrawal_method == 'usdt':
                message += "₿ <b>USDT Wallet Details:</b>\n"

                usdt_address = account_info.get('usdt_address', '')
                if usdt_address:
                    message += f"▫️ <b>USDT Address:</b> <code>{usdt_address}</code>\n"
                else:
                    message += f"▫️ <b>USDT Address:</b> <i>Not provided</i>\n"

                binance_id = account_info.get('binance_id', '')
                if binance_id:
                    message += f"▫️ <b>Binance ID:</b> {binance_id}\n"
                else:
                    message += f"▫️ <b>Binance ID:</b> <i>Not provided</i>\n"

            # Request timestamp (if available from withdrawal reports)
            withdrawal_reports = user.get('withdrawal_reports', [])
            request_time = "Unknown"
            for report in withdrawal_reports:
                if (report.get('amount') == pending_amount and
                    report.get('status') not in ['Passed', 'Failed']):
                    timestamp = report.get('timestamp', report.get('created_at', 0))
                    if timestamp:
                        try:
                            request_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
                        except:
                            request_time = report.get('date', 'Unknown')
                    break

            message += f"\n⏰ <b>Request Time:</b> {request_time}\n\n"

            # Warning and confirmation
            message += "⚠️ <b>Please verify all details before approval!</b>\n\n"
            message += "🔴 <b>Are you sure you want to approve this withdrawal?</b>"

            # Create keyboard with confirmation options
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Confirm Approval', callback_data=f'confirm_approve_withdrawal_{target_user_id}'),
                    InlineKeyboardButton('❌ Cancel', callback_data='passUserWithdrawal')
                ],
                [
                    InlineKeyboardButton('📋 View Full Details', callback_data=f'view_user_full_details_{target_user_id}')
                ]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_withdrawal_approval_confirmation: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            await query.answer("❌ Error loading withdrawal confirmation.", show_alert=True)

    async def handle_confirmed_withdrawal_approval(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int):
        """Handle confirmed withdrawal approval after confirmation dialog"""
        query = update.callback_query
        admin_id = query.from_user.id

        try:
            if not await is_admin_async(admin_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            await query.answer("Processing withdrawal approval...")

            from services.withdrawal_service import WithdrawalService
            from services.user_service import UserService
            from datetime import datetime

            withdrawal_service = WithdrawalService()
            user_service = UserService()

            # Get user data for confirmation message
            user = await user_service.get_user(target_user_id)
            if not user:
                await query.answer("❌ User not found.", show_alert=True)
                return

            pending_amount = user.get('withdraw_under_review', 0)
            if pending_amount <= 0:
                await query.answer("❌ No pending withdrawal found for this user.", show_alert=True)
                return

            # Process the approval
            success = await withdrawal_service.approve_withdrawal(target_user_id, admin_id)

            if success:
                # Show success message with details
                admin_name = query.from_user.first_name or "Admin"
                user_name = user.get('first_name', 'Unknown')

                success_message = "✅ <b>Withdrawal Approved Successfully!</b>\n\n"
                success_message += f"👤 <b>User:</b> {user_name} (ID: {target_user_id})\n"
                success_message += f"💰 <b>Amount:</b> ₹{pending_amount:,}\n"
                success_message += f"👨‍💼 <b>Approved by:</b> {admin_name}\n"
                success_message += f"⏰ <b>Processed at:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                success_message += "📧 User has been notified of the approval."

                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton('✅ Approve More', callback_data='passUserWithdrawal'),
                        InlineKeyboardButton('📊 View Pending', callback_data='viewPendingWithdrawals')
                    ],
                    [
                        InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')
                    ]
                ])

                await query.edit_message_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                # Show success alert
                await query.answer("✅ Withdrawal approved and user notified!", show_alert=True)

                logger.info(f"Admin {admin_id} ({admin_name}) approved withdrawal for user {target_user_id} (₹{pending_amount})")

            else:
                # Show error message
                error_message = "❌ <b>Withdrawal Approval Failed</b>\n\n"
                error_message += f"Failed to approve withdrawal for user {target_user_id}.\n"
                error_message += "This could be due to:\n"
                error_message += "• User no longer has pending withdrawal\n"
                error_message += "• Database connection issue\n"
                error_message += "• User account issue\n\n"
                error_message += "Please try again or contact technical support."

                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton('🔄 Try Again', callback_data=f'approve_withdrawal_{target_user_id}'),
                        InlineKeyboardButton('📊 View Pending', callback_data='viewPendingWithdrawals')
                    ],
                    [
                        InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')
                    ]
                ])

                await query.edit_message_text(
                    error_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                await query.answer("❌ Failed to approve withdrawal. Please try again.", show_alert=True)

                logger.error(f"Failed to approve withdrawal for user {target_user_id} by admin {admin_id}")

        except Exception as e:
            logger.error(f"Error in handle_confirmed_withdrawal_approval: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            await query.answer("❌ Error processing withdrawal approval.", show_alert=True)

    async def handle_view_user_full_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, target_user_id: int):
        """Handle viewing full user details for withdrawal verification"""
        query = update.callback_query
        admin_id = query.from_user.id

        try:
            if not await is_admin_async(admin_id):
                await query.answer("❌ Access denied.", show_alert=True)
                return

            await query.answer("Loading full user details...")

            from services.user_service import UserService
            from services.admin_service import AdminService
            from datetime import datetime

            user_service = UserService()
            admin_service = AdminService()

            # Get user data
            user = await user_service.get_user(target_user_id)
            if not user:
                await query.answer("❌ User not found.", show_alert=True)
                return

            # Get account information
            account_info = user.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'bank')
            pending_amount = user.get('withdraw_under_review', 0)

            # Build detailed message
            message = "📋 <b>Complete User Details</b>\n\n"

            # Basic User Information
            message += "👤 <b>User Information:</b>\n"
            message += f"▫️ <b>Name:</b> {user.get('first_name', 'Unknown')}\n"
            if user.get('last_name'):
                message += f"▫️ <b>Last Name:</b> {user.get('last_name')}\n"
            if user.get('username'):
                message += f"▫️ <b>Username:</b> @{user.get('username')}\n"
            message += f"▫️ <b>User ID:</b> <code>{target_user_id}</code>\n"
            message += f"▫️ <b>Language:</b> {user.get('language_code', 'Unknown')}\n\n"

            # Financial Information
            message += "💰 <b>Financial Information:</b>\n"
            message += f"▫️ <b>Current Balance:</b> ₹{user.get('balance', 0):,}\n"
            message += f"▫️ <b>Total Earned:</b> ₹{user.get('total_earned', 0):,}\n"
            message += f"▫️ <b>Successful Withdrawals:</b> ₹{user.get('successful_withdraw', 0):,}\n"
            message += f"▫️ <b>Pending Withdrawal:</b> ₹{pending_amount:,}\n"
            message += f"▫️ <b>Total Referrals:</b> {user.get('total_referrals', 0)}\n\n"

            # Account Details
            if withdrawal_method == 'bank':
                message += "🏦 <b>Bank Account Details:</b>\n"
                message += f"▫️ <b>Account Holder:</b> {account_info.get('name', 'Not provided')}\n"
                message += f"▫️ <b>Account Number:</b> <code>{account_info.get('account_number', 'Not provided')}</code>\n"
                message += f"▫️ <b>IFSC Code:</b> {account_info.get('ifsc', 'Not provided')}\n"
                message += f"▫️ <b>Mobile Number:</b> {account_info.get('mobile_number', 'Not provided')}\n"
                message += f"▫️ <b>Email:</b> {account_info.get('email', 'Not provided')}\n"
            elif withdrawal_method == 'usdt':
                message += "₿ <b>USDT Wallet Details:</b>\n"
                message += f"▫️ <b>USDT Address:</b> <code>{account_info.get('usdt_address', 'Not provided')}</code>\n"
                message += f"▫️ <b>Binance ID:</b> {account_info.get('binance_id', 'Not provided')}\n"

            # Registration and Activity
            message += f"\n📅 <b>Account Activity:</b>\n"

            # Registration date
            registration_date = user.get('registration_date', 'Unknown')
            if registration_date != 'Unknown':
                try:
                    if isinstance(registration_date, (int, float)):
                        reg_date = datetime.fromtimestamp(registration_date).strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        reg_date = str(registration_date)
                    message += f"▫️ <b>Registered:</b> {reg_date}\n"
                except:
                    message += f"▫️ <b>Registered:</b> {registration_date}\n"
            else:
                message += f"▫️ <b>Registered:</b> Unknown\n"

            # Last activity
            last_activity = user.get('last_activity', 'Unknown')
            if last_activity != 'Unknown':
                try:
                    if isinstance(last_activity, (int, float)):
                        last_act = datetime.fromtimestamp(last_activity).strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        last_act = str(last_activity)
                    message += f"▫️ <b>Last Activity:</b> {last_act}\n"
                except:
                    message += f"▫️ <b>Last Activity:</b> {last_activity}\n"
            else:
                message += f"▫️ <b>Last Activity:</b> Unknown\n"

            # Withdrawal History Summary
            withdrawal_reports = user.get('withdrawal_reports', [])
            if withdrawal_reports:
                passed_count = len([w for w in withdrawal_reports if w.get('status') == 'Passed'])
                failed_count = len([w for w in withdrawal_reports if w.get('status') == 'Failed'])
                message += f"\n📊 <b>Withdrawal History:</b>\n"
                message += f"▫️ <b>Total Requests:</b> {len(withdrawal_reports)}\n"
                message += f"▫️ <b>Approved:</b> {passed_count}\n"
                message += f"▫️ <b>Rejected:</b> {failed_count}\n"

            # Create keyboard
            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('✅ Approve Withdrawal', callback_data=f'confirm_approve_withdrawal_{target_user_id}'),
                    InlineKeyboardButton('❌ Reject Withdrawal', callback_data=f'reject_withdrawal_{target_user_id}')
                ],
                [
                    InlineKeyboardButton('📊 View Withdrawal History', callback_data=f'user_withdrawal_records_{target_user_id}')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Confirmation', callback_data=f'approve_withdrawal_{target_user_id}')
                ]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_view_user_full_details: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            await query.answer("❌ Error loading user details.", show_alert=True)

    async def handle_withdrawal_history(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle withdrawal history - display recent withdrawal history"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            # Answer callback query immediately to prevent timeout
            await query.answer("Loading withdrawal history...")

            from services.user_service import UserService
            user_service = UserService()

            # Get all users and their withdrawal reports
            all_users = await user_service.get_all_users()
            all_withdrawals = []

            for user in all_users:
                withdrawal_reports = user.get('withdrawal_reports', [])
                for report in withdrawal_reports:
                    withdrawal_data = {
                        'user_id': user['user_id'],
                        'first_name': user.get('first_name', 'Unknown'),
                        'username': user.get('username', ''),
                        'amount': report.get('amount', 0),
                        'status': report.get('status', 'Unknown'),
                        'method': report.get('method', 'bank'),
                        'date': report.get('date', 'Unknown'),
                        'created_at': report.get('created_at', 0)
                    }
                    all_withdrawals.append(withdrawal_data)

            # Sort by creation time (most recent first)
            all_withdrawals.sort(key=lambda x: x.get('created_at', 0), reverse=True)

            if not all_withdrawals:
                await query.edit_message_text(
                    "ℹ️ <b>No Withdrawal History</b>\n\n"
                    "There are no withdrawal records in the system yet.",
                    parse_mode='HTML',
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')]
                    ])
                )
                return

            # Calculate statistics
            total_withdrawals = len(all_withdrawals)
            approved_withdrawals = [w for w in all_withdrawals if w['status'] == 'Passed']
            rejected_withdrawals = [w for w in all_withdrawals if w['status'] == 'Failed']
            pending_withdrawals = [w for w in all_withdrawals if w['status'] == 'Under review']

            total_approved_amount = sum(w['amount'] for w in approved_withdrawals)
            total_rejected_amount = sum(w['amount'] for w in rejected_withdrawals)
            total_pending_amount = sum(w['amount'] for w in pending_withdrawals)

            # Create message
            message = "📋 <b>Withdrawal History</b>\n\n"
            message += f"📊 <b>Statistics:</b>\n"
            message += f"• Total Requests: {total_withdrawals}\n"
            message += f"• ✅ Approved: {len(approved_withdrawals)} (₹{total_approved_amount})\n"
            message += f"• ❌ Rejected: {len(rejected_withdrawals)} (₹{total_rejected_amount})\n"
            message += f"• ⏳ Pending: {len(pending_withdrawals)} (₹{total_pending_amount})\n\n"

            message += "📝 <b>Recent Withdrawals:</b>\n"

            # Show recent withdrawals
            for i, withdrawal in enumerate(all_withdrawals[:20], 1):  # Show first 20
                # Escape HTML entities to prevent parsing errors
                from utils.helpers import escape_html

                first_name = escape_html(withdrawal['first_name'])
                username = escape_html(withdrawal['username']) if withdrawal['username'] else ""
                date = escape_html(withdrawal['date'])
                status = escape_html(withdrawal['status'])

                user_display = first_name
                if username:
                    user_display += f" (@{username})"

                status_icon = "✅" if withdrawal['status'] == 'Passed' else "❌" if withdrawal['status'] == 'Failed' else "⏳"
                method_icon = "🏦" if withdrawal['method'] == 'bank' else "₿"

                message += f"{i}. {status_icon} {method_icon} <b>{user_display}</b>\n"
                message += f"   💰 ₹{withdrawal['amount']} • {date}\n"
                message += f"   📱 ID: {withdrawal['user_id']} • Status: {status}\n\n"

            if len(all_withdrawals) > 20:
                message += f"<i>... and {len(all_withdrawals) - 20} more records</i>\n\n"

            keyboard = InlineKeyboardMarkup([
                [
                    InlineKeyboardButton('📊 View Pending', callback_data='viewPendingWithdrawals'),
                    InlineKeyboardButton('🔄 Refresh History', callback_data='withdrawalHistory')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manageWithdrawals')
                ]
            ])

            await query.edit_message_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in handle_withdrawal_history: {e}")
            await query.answer("❌ Error loading withdrawal history.", show_alert=True)

    async def handle_reset_user_account(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle reset user account details request"""
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await update.message.reply_text(ADMIN_ACCESS_DENIED, parse_mode='HTML')
                return

            # Set session for user ID input
            from handlers.session_handlers import SessionHandlers
            session_handlers = SessionHandlers()

            await session_handlers.set_user_session(user_id, {
                'step': 'reset_user_account_id',
                'data': {}
            })

            message = "🔄 <b>Reset User Account Details</b>\n\n"
            message += "Please enter the User ID or Username of the user whose account details you want to reset.\n\n"
            message += "📝 <b>You can enter:</b>\n"
            message += "• User ID (e.g., *********)\n"
            message += "• Username (e.g., @username)\n\n"
            message += "⚠️ <b>Warning:</b> This will clear all withdrawal method settings and account details for the user.\n\n"
            message += "💡 <b>Note:</b> Users can now change withdrawal methods themselves, so this reset is mainly for clearing corrupted data.\n\n"
            message += "Send /cancel to cancel this process."

            await update.message.reply_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_reset_user_account: {e}")
            await update.message.reply_text("❌ Error initiating account reset. Please try again.")

    # ==================== CUSTOM REFERRAL HELPER METHODS ====================

    async def _show_custom_referral_help(self, update: Update):
        """Show custom referral help (matching PHP version exactly)"""
        from models.custom_referral import CustomReferralModel

        help_text = CustomReferralModel.format_custom_referral_help_message()
        keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

        await update.message.reply_text(help_text, reply_markup=keyboard, parse_mode='HTML')

    async def _handle_custom_referral_list(self, update: Update):
        """Handle custom referral list (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()

            # Get all custom referrals
            referrals = await custom_referral_service.get_all_custom_referrals()

            # Format message
            message = CustomReferralModel.format_custom_referral_list_message(referrals)
            keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_list: {e}")
            await update.message.reply_text("❌ Error loading custom referral links.", parse_mode='HTML')

    async def _handle_custom_referral_create(self, update: Update, param: str, user_id_str: str):
        """Handle custom referral create (matching PHP version exactly)"""
        try:
            # Validate user ID
            try:
                user_id = int(user_id_str)
            except ValueError:
                await update.message.reply_text("❌ Invalid user ID. Please provide a valid number.", parse_mode='HTML')
                return

            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Create custom referral
            result = await custom_referral_service.create_custom_referral(param, user_id, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_creation_success_message(
                    result['custom_param'],
                    result['custom_link'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_create: {e}")
            await update.message.reply_text("❌ Error creating custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_edit(self, update: Update, old_param: str, new_param: str):
        """Handle custom referral edit (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Update custom referral
            result = await custom_referral_service.update_custom_referral(old_param, new_param, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_update_success_message(
                    result['old_param'],
                    result['new_param'],
                    result['custom_link'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_edit: {e}")
            await update.message.reply_text("❌ Error updating custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_delete(self, update: Update, param: str):
        """Handle custom referral delete (matching PHP version exactly)"""
        try:
            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()
            admin_id = update.effective_user.id

            # Delete custom referral
            result = await custom_referral_service.delete_custom_referral(param, admin_id)

            if result['success']:
                # Show success message
                message = CustomReferralModel.format_custom_referral_deletion_success_message(
                    result['param'],
                    result['user_name'],
                    result['user_id']
                )
                keyboard = CustomReferralModel.create_custom_referral_success_keyboard()

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(f"❌ {result['message']}", parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_delete: {e}")
            await update.message.reply_text("❌ Error deleting custom referral link.", parse_mode='HTML')

    async def _handle_custom_referral_view(self, update: Update, user_id_str: str):
        """Handle custom referral view (matching PHP version exactly)"""
        try:
            # Validate user ID
            try:
                user_id = int(user_id_str)
            except ValueError:
                await update.message.reply_text("❌ Invalid user ID. Please provide a valid number.", parse_mode='HTML')
                return

            from services.custom_referral_service import CustomReferralService
            from models.custom_referral import CustomReferralModel

            custom_referral_service = CustomReferralService()

            # Get custom referrals for user
            referrals = await custom_referral_service.get_custom_referrals_by_user(user_id)

            if not referrals:
                await update.message.reply_text(f"❌ No custom referral links found for user {user_id}.", parse_mode='HTML')
                return

            # Format message for specific user
            message = f"🔗 <b>Custom Referral Links for User {user_id}</b>\n\n"

            for i, referral in enumerate(referrals, 1):
                param = referral.get('custom_param', 'Unknown')
                clicks = referral.get('clicks', 0)
                conversions = referral.get('referrals', 0)
                active = referral.get('active', True)

                status_emoji = "✅" if active else "❌"

                message += f"<b>{i}.</b> {status_emoji} <code>{param}</code>\n"
                message += f"   📊 {clicks} clicks, {conversions} referrals\n\n"

            keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_custom_referral_view: {e}")
            await update.message.reply_text("❌ Error viewing custom referral links.", parse_mode='HTML')

    async def handle_custom_referral_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE, action: str):
        """Handle custom referral callback actions (matching PHP version exactly)"""
        query = update.callback_query
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer(ADMIN_ACCESS_DENIED, show_alert=True)
                return

            if action == 'list':
                from services.custom_referral_service import CustomReferralService
                from models.custom_referral import CustomReferralModel

                custom_referral_service = CustomReferralService()

                # Get all custom referrals
                referrals = await custom_referral_service.get_all_custom_referrals()

                # Format message
                message = CustomReferralModel.format_custom_referral_list_message(referrals)
                keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
            elif action == 'help':
                from models.custom_referral import CustomReferralModel

                help_text = CustomReferralModel.format_custom_referral_help_message()
                keyboard = CustomReferralModel.create_custom_referral_management_keyboard()

                await query.edit_message_text(help_text, reply_markup=keyboard, parse_mode='HTML')
            else:
                # Default action - show custom referral management
                await self.handle_custom_referral_link(update, context)

        except Exception as e:
            logger.error(f"Error in handle_custom_referral_callback: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    # ==================== DATABASE CLEANUP HANDLERS ====================

    async def handle_clean_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /clean command for database cleanup"""
        user_id = update.effective_user.id

        try:
            # Check admin access
            if not await is_admin_async(user_id):
                logger.debug(f"User {user_id} denied access to /clean command - not an admin")
                return

            # Show database cleanup menu
            await self.handle_database_cleanup(update, context)

        except Exception as e:
            logger.error(f"Error in handle_clean_command: {e}")
            if update.message:
                await update.message.reply_text(
                    "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                    parse_mode='HTML'
                )

    async def handle_database_cleanup(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle database cleanup main menu with background processing support"""
        query = update.callback_query if update.callback_query else None
        user_id = update.effective_user.id

        try:
            if not await is_admin_async(user_id):
                if query:
                    await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            if query:
                await query.answer()

            # Check if cleanup is already running
            is_running = await self.background_cleanup_manager.is_cleanup_running()
            running_info = None
            if is_running:
                running_info = await self.background_cleanup_manager.get_running_cleanup_info()

            message = "🗑️ <b>Database Cleanup</b>\n\n"

            if is_running:
                message += "⚠️ <b>A cleanup process is currently running!</b>\n\n"
                if running_info:
                    progress = running_info.get('progress', {})
                    processed = progress.get('processed_users', 0)
                    total = progress.get('total_users', 0)
                    percentage = progress.get('percentage', 0)

                    message += f"📊 <b>Progress:</b> {processed:,}/{total:,} users ({percentage:.1f}%)\n"
                    message += f"🗑️ <b>Found Inactive:</b> {progress.get('removed_users', 0):,} users\n"
                    message += f"🚫 <b>Blocked:</b> {progress.get('blocked_users', 0):,}\n"
                    message += f"❌ <b>Deleted:</b> {progress.get('deleted_accounts', 0):,}\n"
                    message += f"🔒 <b>Inaccessible:</b> {progress.get('inaccessible_users', 0):,}\n\n"

                keyboard = [
                    [
                        InlineKeyboardButton('🔄 Refresh Status', callback_data='cleanup_status'),
                        InlineKeyboardButton('⏹️ Stop Cleanup', callback_data='cleanup_stop')
                    ],
                    [
                        InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')
                    ]
                ]
            else:
                message += "This tool helps you clean up inactive users from the database:\n\n"
                message += "• <b>Blocked Users:</b> Users who blocked the bot\n"
                message += "• <b>Deleted Accounts:</b> Users who deleted their Telegram accounts\n"
                message += "• <b>Inaccessible Users:</b> Users whose accounts are no longer accessible\n\n"
                message += "⚠️ <b>Warning:</b> This action cannot be undone. Users will be permanently removed from the database.\n\n"
                message += "🚀 <b>Background Processing:</b> Cleanup runs in the background to keep the bot responsive.\n\n"
                message += "Choose an option below:"

                keyboard = [
                    [
                        InlineKeyboardButton('📊 Preview Cleanup', callback_data='cleanup_preview'),
                        InlineKeyboardButton('🗑️ Start Cleanup', callback_data='cleanup_start')
                    ],
                    [
                        InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')
                    ]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            if query:
                await self._safe_edit_message(query, message, reply_markup, context)
            else:
                await update.message.reply_text(message, reply_markup=reply_markup, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_database_cleanup: {e}")
            if query:
                await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_cleanup_preview(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle cleanup preview request using background processing"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Check if cleanup is already running
            if await self.background_cleanup_manager.is_cleanup_running():
                await query.answer("⚠️ A cleanup process is already running. Please wait for it to complete.", show_alert=True)
                return

            # Show loading message
            loading_message = "📊 <b>Starting Background Preview...</b>\n\n"
            loading_message += "🚀 Preview is running in the background to keep the bot responsive.\n"
            loading_message += "⏳ This may take a few moments for large databases.\n\n"
            loading_message += "You will be notified when the preview is complete."

            loading_keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('🔄 Check Status', callback_data='cleanup_status')],
                [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
            ])

            await query.edit_message_text(loading_message, reply_markup=loading_keyboard, parse_mode='HTML')

            # Start background preview process
            result = await self.background_cleanup_manager.start_cleanup_process(user_id, preview_only=True)

            if result['success']:
                # Process started successfully
                success_message = "✅ <b>Background Preview Started</b>\n\n"
                success_message += f"🆔 <b>Process ID:</b> {result['cleanup_id']}\n"
                success_message += "🚀 Preview is running in the background.\n\n"
                success_message += "Use 'Check Status' to monitor progress or wait for completion notification."

                success_keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Check Status', callback_data='cleanup_status')],
                    [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
                ])

                await query.edit_message_text(success_message, reply_markup=success_keyboard, parse_mode='HTML')
            else:
                # Failed to start process
                error_message = "❌ <b>Failed to Start Preview</b>\n\n"
                error_message += f"Error: {result.get('message', 'Unknown error')}\n\n"
                error_message += "Please try again later."

                error_keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='cleanup_preview')],
                    [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
                ])

                await query.edit_message_text(error_message, reply_markup=error_keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_cleanup_preview: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_cleanup_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle cleanup start request with confirmation"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Check if cleanup is already running
            if await self.background_cleanup_manager.is_cleanup_running():
                await query.answer("⚠️ A cleanup process is already running. Please wait for it to complete.", show_alert=True)
                return

            # Show confirmation dialog
            message = "⚠️ <b>Confirm Database Cleanup</b>\n\n"
            message += "🚨 <b>WARNING:</b> This action will permanently remove inactive users from the database!\n\n"
            message += "The following users will be removed:\n"
            message += "• Users who have blocked the bot\n"
            message += "• Users who have deleted their Telegram accounts\n"
            message += "• Users whose accounts are no longer accessible\n\n"
            message += "❌ <b>This action cannot be undone!</b>\n"
            message += "🚀 <b>The cleanup will run in the background to keep the bot responsive.</b>\n\n"
            message += "Are you sure you want to proceed?"

            keyboard = [
                [
                    InlineKeyboardButton('✅ Yes, Start Cleanup', callback_data='cleanup_confirm'),
                    InlineKeyboardButton('❌ Cancel', callback_data='database_cleanup')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)
            await query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_cleanup_start: {e}")
            await query.answer("❌ Something went wrong. Please try again later.", show_alert=True)

    async def handle_cleanup_confirm(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle confirmed cleanup execution using background processing"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Check if cleanup is already running
            if await self.background_cleanup_manager.is_cleanup_running():
                await query.answer("⚠️ A cleanup process is already running. Please wait for it to complete.", show_alert=True)
                return

            # Show starting message
            starting_message = "🚀 <b>Starting Background Cleanup...</b>\n\n"
            starting_message += "⏳ Initializing background cleanup process...\n"
            starting_message += "🚀 The cleanup will run in the background to keep the bot responsive.\n\n"
            starting_message += "You will be notified when the cleanup is complete."

            starting_keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('🔄 Check Status', callback_data='cleanup_status')],
                [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
            ])

            await query.edit_message_text(starting_message, reply_markup=starting_keyboard, parse_mode='HTML')

            # Start background cleanup process
            result = await self.background_cleanup_manager.start_cleanup_process(user_id, preview_only=False)

            if result['success']:
                # Process started successfully
                success_message = "✅ <b>Background Cleanup Started</b>\n\n"
                success_message += f"🆔 <b>Process ID:</b> {result['cleanup_id']}\n"
                success_message += "🚀 Cleanup is running in the background.\n"
                success_message += "📱 The bot remains fully responsive during cleanup.\n\n"
                success_message += "You will receive a notification when the cleanup is complete.\n"
                success_message += "Use 'Check Status' to monitor progress."

                success_keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Check Status', callback_data='cleanup_status')],
                    [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
                ])

                await query.edit_message_text(success_message, reply_markup=success_keyboard, parse_mode='HTML')
            else:
                # Failed to start process
                error_message = "❌ <b>Failed to Start Cleanup</b>\n\n"
                error_message += f"Error: {result.get('message', 'Unknown error')}\n\n"

                if 'running_process' in result:
                    error_message += "A cleanup process is already running. Please wait for it to complete."
                else:
                    error_message += "Please try again later."

                error_keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='cleanup_start')],
                    [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
                ])

                await query.edit_message_text(error_message, reply_markup=error_keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_cleanup_confirm: {e}")
            await query.answer("❌ Something went wrong during cleanup startup. Please try again later.", show_alert=True)

    async def handle_cleanup_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle cleanup status check request"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get current cleanup status
            status = await self.background_cleanup_manager.get_cleanup_status()

            if not status:
                message = "ℹ️ <b>No Active Cleanup Process</b>\n\n"
                message += "There is no cleanup process currently running.\n"
                message += "You can start a new cleanup from the main menu."

                keyboard = [
                    [InlineKeyboardButton('🗑️ Start New Cleanup', callback_data='cleanup_start')],
                    [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
                ]
            else:
                cleanup_status = status.get('status', 'unknown')

                if cleanup_status in ['completed', 'failed', 'stopped']:
                    # Process completed
                    if cleanup_status == 'completed':
                        result = status.get('result', {})

                        if status.get('preview_only'):
                            message = "✅ <b>Preview Completed</b>\n\n"
                            message += f"📈 <b>Total Users:</b> {result.get('total_users', 0):,}\n"
                            message += f"🔍 <b>Sample Checked:</b> {result.get('sample_size', 0):,}\n"
                            message += f"🗑️ <b>Estimated Removals:</b> {result.get('estimated_removals', 0):,}\n"
                            message += f"📊 <b>Estimated Percentage:</b> {result.get('estimated_percentage', 0)}%\n\n"

                            if result.get('estimated_removals', 0) > 0:
                                message += "Would you like to proceed with the actual cleanup?"
                                keyboard = [
                                    [InlineKeyboardButton('🗑️ Start Cleanup', callback_data='cleanup_start')],
                                    [InlineKeyboardButton('🔄 New Preview', callback_data='cleanup_preview')]
                                ]
                            else:
                                message += "✅ No inactive users detected in the sample."
                                keyboard = [
                                    [InlineKeyboardButton('🔄 New Preview', callback_data='cleanup_preview')]
                                ]
                        else:
                            message = "✅ <b>Cleanup Completed Successfully!</b>\n\n"
                            message += f"📊 <b>Total Users Processed:</b> {result.get('processed_users', 0):,}\n"
                            message += f"🗑️ <b>Users Removed:</b> {result.get('removed_users', 0):,}\n"
                            message += f"🚫 <b>Blocked Bot:</b> {result.get('blocked_users', 0):,}\n"
                            message += f"❌ <b>Deleted Accounts:</b> {result.get('deleted_accounts', 0):,}\n"
                            message += f"🔒 <b>Inaccessible:</b> {result.get('inaccessible_users', 0):,}\n"

                            if result.get('errors', 0) > 0:
                                message += f"⚠️ <b>Errors:</b> {result.get('errors', 0):,}\n"

                            message += f"\n💾 Database cleanup completed successfully!"

                            keyboard = [
                                [InlineKeyboardButton('🗑️ Start New Cleanup', callback_data='cleanup_start')]
                            ]

                    elif cleanup_status == 'failed':
                        message = "❌ <b>Cleanup Failed</b>\n\n"
                        message += f"Error: {status.get('error', 'Unknown error')}\n\n"
                        message += "Please check the logs and try again."

                        keyboard = [
                            [InlineKeyboardButton('🔄 Try Again', callback_data='cleanup_start')]
                        ]

                    else:  # stopped
                        message = "⏹️ <b>Cleanup Stopped</b>\n\n"
                        message += "The cleanup process was stopped by an admin.\n"
                        message += "You can start a new cleanup if needed."

                        keyboard = [
                            [InlineKeyboardButton('🗑️ Start New Cleanup', callback_data='cleanup_start')]
                        ]

                    # Send completion notification if not already sent
                    await self.background_cleanup_manager.send_completion_notification(user_id, status)

                else:
                    # Process is running
                    progress = status.get('progress', {})
                    processed = progress.get('processed_users', 0)
                    total = progress.get('total_users', 0)
                    percentage = progress.get('percentage', 0)

                    message = "🔄 <b>Cleanup in Progress</b>\n\n"
                    message += f"📊 <b>Progress:</b> {processed:,}/{total:,} users ({percentage:.1f}%)\n"
                    message += f"🗑️ <b>Found Inactive:</b> {progress.get('removed_users', 0):,} users\n"
                    message += f"🚫 <b>Blocked:</b> {progress.get('blocked_users', 0):,}\n"
                    message += f"❌ <b>Deleted:</b> {progress.get('deleted_accounts', 0):,}\n"
                    message += f"🔒 <b>Inaccessible:</b> {progress.get('inaccessible_users', 0):,}\n"

                    if progress.get('errors', 0) > 0:
                        message += f"⚠️ <b>Errors:</b> {progress.get('errors', 0):,}\n"

                    message += f"\n🚀 Running in background... Bot remains responsive."

                    keyboard = [
                        [InlineKeyboardButton('🔄 Refresh Status', callback_data='cleanup_status')],
                        [InlineKeyboardButton('⏹️ Stop Cleanup', callback_data='cleanup_stop')]
                    ]

                keyboard.append([InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')])

            reply_markup = InlineKeyboardMarkup(keyboard)
            await query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_cleanup_status: {e}")
            await query.answer("❌ Something went wrong checking status. Please try again later.", show_alert=True)

    async def handle_cleanup_stop(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle cleanup stop request"""
        query = update.callback_query
        user_id = query.from_user.id

        try:
            if not await is_admin_async(user_id):
                await query.answer("❌ Access denied. Admin only.", show_alert=True)
                return

            await query.answer()

            # Get running cleanup info
            running_info = await self.background_cleanup_manager.get_running_cleanup_info()

            if not running_info:
                await query.answer("ℹ️ No cleanup process is currently running.", show_alert=True)
                return

            cleanup_id = running_info.get('cleanup_id')

            if not cleanup_id:
                await query.answer("❌ Could not identify the running cleanup process.", show_alert=True)
                return

            # Stop the cleanup process
            result = await self.background_cleanup_manager.stop_cleanup_process(cleanup_id)

            if result['success']:
                message = "⏹️ <b>Cleanup Process Stopped</b>\n\n"
                message += "The background cleanup process has been stopped successfully.\n"
                message += "You can start a new cleanup if needed."

                keyboard = [
                    [InlineKeyboardButton('🗑️ Start New Cleanup', callback_data='cleanup_start')],
                    [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
                ]
            else:
                message = "❌ <b>Failed to Stop Cleanup</b>\n\n"
                message += f"Error: {result.get('message', 'Unknown error')}\n\n"
                message += "The process may have already completed or stopped."

                keyboard = [
                    [InlineKeyboardButton('🔄 Check Status', callback_data='cleanup_status')],
                    [InlineKeyboardButton('↩️ Back to Database Cleanup', callback_data='database_cleanup')]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)
            await query.edit_message_text(message, reply_markup=reply_markup, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in handle_cleanup_stop: {e}")
            await query.answer("❌ Something went wrong stopping the cleanup. Please try again later.", show_alert=True)

    # Legacy methods removed - cleanup now runs in background
    # These methods are no longer needed as cleanup runs as a separate process

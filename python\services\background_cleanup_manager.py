"""
Background Cleanup Process Manager
Manages background cleanup processes, status monitoring, and notifications
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

from telegram import Bot
from telegram.error import TelegramError

from config.settings import settings
from utils.helpers import get_current_timestamp, send_safe_message

logger = logging.getLogger(__name__)

class BackgroundCleanupManager:
    """Manages background cleanup processes"""
    
    def __init__(self, bot: Bot = None):
        self.bot = bot or Bot(settings.BOT_TOKEN)
        self.processes: Dict[str, subprocess.Popen] = {}
        self.status_dir = Path("cleanup_status")
        self.status_dir.mkdir(exist_ok=True)
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)
    
    async def start_cleanup_process(self, admin_id: int, preview_only: bool = False) -> Dict[str, Any]:
        """
        Start a background cleanup process
        
        Args:
            admin_id: Admin user ID who initiated the cleanup
            preview_only: Whether to run preview only
            
        Returns:
            Dict with process information
        """
        try:
            # Check if cleanup is already running
            if await self.is_cleanup_running():
                running_process = await self.get_running_cleanup_info()
                return {
                    'success': False,
                    'message': 'A cleanup process is already running',
                    'running_process': running_process
                }
            
            # Generate unique cleanup ID
            cleanup_id = f"cleanup_{int(time.time())}_{str(uuid.uuid4())[:8]}"
            
            # Prepare command
            python_executable = sys.executable
            script_path = Path(__file__).parent.parent / "background_cleanup.py"
            
            cmd = [
                python_executable,
                str(script_path),
                "--cleanup-id", cleanup_id,
                "--admin-id", str(admin_id)
            ]
            
            if preview_only:
                cmd.append("--preview-only")
            
            # Start the background process
            logger.info(f"Starting background cleanup process: {cleanup_id}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=Path(__file__).parent.parent
            )
            
            # Store process reference
            self.processes[cleanup_id] = process
            
            # Wait a moment to ensure process started
            await asyncio.sleep(1)
            
            # Check if process is still running
            if process.poll() is not None:
                # Process already terminated, check for errors
                stdout, stderr = process.communicate()
                logger.error(f"Background process failed to start: {stderr}")
                return {
                    'success': False,
                    'message': f'Failed to start cleanup process: {stderr}',
                    'cleanup_id': cleanup_id
                }
            
            logger.info(f"Background cleanup process started successfully: {cleanup_id}")
            
            return {
                'success': True,
                'cleanup_id': cleanup_id,
                'message': 'Background cleanup process started successfully',
                'preview_only': preview_only
            }
            
        except Exception as e:
            logger.error(f"Error starting background cleanup process: {e}")
            return {
                'success': False,
                'message': f'Failed to start cleanup process: {str(e)}'
            }
    
    async def get_cleanup_status(self, cleanup_id: str = None) -> Optional[Dict[str, Any]]:
        """
        Get status of a cleanup process
        
        Args:
            cleanup_id: Specific cleanup ID, or None for any running cleanup
            
        Returns:
            Status information or None if not found
        """
        try:
            if cleanup_id:
                status_file = Path(f"cleanup_status_{cleanup_id}.json")
            else:
                # Find any running cleanup
                status_files = list(Path(".").glob("cleanup_status_*.json"))
                if not status_files:
                    return None
                status_file = status_files[0]  # Get the first one
            
            if not status_file.exists():
                return None
            
            with open(status_file, 'r', encoding='utf-8') as f:
                status = json.load(f)
            
            # Check if process is still running
            if cleanup_id and cleanup_id in self.processes:
                process = self.processes[cleanup_id]
                if process.poll() is None:
                    status['process_running'] = True
                else:
                    status['process_running'] = False
                    # Clean up finished process
                    del self.processes[cleanup_id]
            else:
                status['process_running'] = False
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting cleanup status: {e}")
            return None
    
    async def is_cleanup_running(self) -> bool:
        """Check if any cleanup process is currently running"""
        try:
            # Check for status files
            status_files = list(Path(".").glob("cleanup_status_*.json"))
            
            for status_file in status_files:
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status = json.load(f)
                    
                    # Check if status indicates running
                    if status.get('status') in ['initializing', 'connected', 'running_preview', 'running_cleanup']:
                        return True
                        
                except Exception as e:
                    logger.debug(f"Error reading status file {status_file}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if cleanup is running: {e}")
            return False
    
    async def get_running_cleanup_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the currently running cleanup"""
        try:
            status_files = list(Path(".").glob("cleanup_status_*.json"))
            
            for status_file in status_files:
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status = json.load(f)
                    
                    # Check if status indicates running
                    if status.get('status') in ['initializing', 'connected', 'running_preview', 'running_cleanup']:
                        return status
                        
                except Exception as e:
                    logger.debug(f"Error reading status file {status_file}: {e}")
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting running cleanup info: {e}")
            return None
    
    async def stop_cleanup_process(self, cleanup_id: str) -> Dict[str, Any]:
        """
        Stop a running cleanup process
        
        Args:
            cleanup_id: Cleanup process ID to stop
            
        Returns:
            Result information
        """
        try:
            if cleanup_id not in self.processes:
                return {
                    'success': False,
                    'message': 'Cleanup process not found or already stopped'
                }
            
            process = self.processes[cleanup_id]
            
            if process.poll() is not None:
                # Process already terminated
                del self.processes[cleanup_id]
                return {
                    'success': True,
                    'message': 'Cleanup process was already stopped'
                }
            
            # Terminate the process
            process.terminate()
            
            # Wait for termination
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                # Force kill if it doesn't terminate gracefully
                process.kill()
                process.wait()
            
            # Clean up
            del self.processes[cleanup_id]
            
            # Update status file
            status_file = Path(f"cleanup_status_{cleanup_id}.json")
            if status_file.exists():
                try:
                    with open(status_file, 'r', encoding='utf-8') as f:
                        status = json.load(f)
                    
                    status.update({
                        'status': 'stopped',
                        'completed_at': get_current_timestamp(),
                        'message': 'Cleanup process was stopped by admin'
                    })
                    
                    with open(status_file, 'w', encoding='utf-8') as f:
                        json.dump(status, f, indent=2, ensure_ascii=False)
                        
                except Exception as e:
                    logger.error(f"Error updating status file after stop: {e}")
            
            logger.info(f"Background cleanup process stopped: {cleanup_id}")
            
            return {
                'success': True,
                'message': 'Cleanup process stopped successfully'
            }
            
        except Exception as e:
            logger.error(f"Error stopping cleanup process: {e}")
            return {
                'success': False,
                'message': f'Failed to stop cleanup process: {str(e)}'
            }
    
    async def cleanup_old_status_files(self, max_age_hours: int = 24):
        """Clean up old status files"""
        try:
            current_time = time.time()
            cutoff_time = current_time - (max_age_hours * 3600)
            
            status_files = list(Path(".").glob("cleanup_status_*.json"))
            cleaned_count = 0
            
            for status_file in status_files:
                try:
                    # Check file modification time
                    if status_file.stat().st_mtime < cutoff_time:
                        # Also check if the cleanup is completed
                        with open(status_file, 'r', encoding='utf-8') as f:
                            status = json.load(f)
                        
                        if status.get('status') in ['completed', 'failed', 'stopped']:
                            status_file.unlink()
                            cleaned_count += 1
                            logger.debug(f"Cleaned up old status file: {status_file}")
                            
                except Exception as e:
                    logger.debug(f"Error cleaning up status file {status_file}: {e}")
                    continue
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old status files")
                
        except Exception as e:
            logger.error(f"Error cleaning up old status files: {e}")
    
    async def send_completion_notification(self, admin_id: int, status: Dict[str, Any]):
        """Send completion notification to admin"""
        try:
            if status.get('status') == 'completed':
                result = status.get('result', {})
                
                if status.get('preview_only'):
                    message = "📊 <b>Cleanup Preview Completed</b>\n\n"
                    message += f"📈 <b>Total Users:</b> {result.get('total_users', 0):,}\n"
                    message += f"🔍 <b>Sample Checked:</b> {result.get('sample_size', 0):,}\n"
                    message += f"🗑️ <b>Estimated Removals:</b> {result.get('estimated_removals', 0):,}\n"
                    message += f"📊 <b>Estimated Percentage:</b> {result.get('estimated_percentage', 0)}%\n\n"
                    message += "✅ Preview completed successfully in background."
                else:
                    message = "✅ <b>Database Cleanup Completed!</b>\n\n"
                    message += f"📊 <b>Total Users Processed:</b> {result.get('processed_users', 0):,}\n"
                    message += f"🗑️ <b>Users Removed:</b> {result.get('removed_users', 0):,}\n"
                    message += f"🚫 <b>Blocked Bot:</b> {result.get('blocked_users', 0):,}\n"
                    message += f"❌ <b>Deleted Accounts:</b> {result.get('deleted_accounts', 0):,}\n"
                    message += f"🔒 <b>Inaccessible:</b> {result.get('inaccessible_users', 0):,}\n"
                    
                    if result.get('errors', 0) > 0:
                        message += f"⚠️ <b>Errors:</b> {result.get('errors', 0):,}\n"
                    
                    message += f"\n💾 <b>Database cleanup completed successfully!</b>\n"
                    message += f"🕒 <b>Completed at:</b> {datetime.fromtimestamp(status.get('completed_at', 0)).strftime('%Y-%m-%d %H:%M:%S')}"
                    
            elif status.get('status') == 'failed':
                message = "❌ <b>Database Cleanup Failed</b>\n\n"
                message += f"Error: {status.get('error', 'Unknown error')}\n\n"
                message += "Please check the logs and try again."
                
            else:
                return  # Don't send notification for other statuses
            
            # Send notification
            await send_safe_message(self.bot, admin_id, message, parse_mode='HTML')
            logger.info(f"Sent cleanup completion notification to admin {admin_id}")
            
        except Exception as e:
            logger.error(f"Error sending completion notification: {e}")

#!/usr/bin/env python3
"""
Background Database Cleanup Process
Runs independently from the main bot to clean up inactive users
"""

import asyncio
import json
import logging
import os
import sys
import time
import argparse
from datetime import datetime
from pathlib import Path

# Add the python directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.database_cleanup_service import DatabaseCleanupService
from config.database import db_manager
from config.settings import settings
from utils.helpers import get_current_timestamp

# Configure logging for background process
def setup_logging(cleanup_id: str):
    """Setup logging for the background cleanup process"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / f"cleanup_{cleanup_id}.log"
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # File handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # Root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return logging.getLogger(__name__)

class BackgroundCleanupProcess:
    """Manages the background cleanup process"""
    
    def __init__(self, cleanup_id: str, admin_id: int, preview_only: bool = False):
        self.cleanup_id = cleanup_id
        self.admin_id = admin_id
        self.preview_only = preview_only
        self.status_file = Path(f"cleanup_status_{cleanup_id}.json")
        self.logger = setup_logging(cleanup_id)
        
    async def run(self):
        """Run the background cleanup process"""
        try:
            self.logger.info(f"Starting background cleanup process: {self.cleanup_id}")
            
            # Initialize status
            await self._update_status({
                'cleanup_id': self.cleanup_id,
                'admin_id': self.admin_id,
                'status': 'initializing',
                'started_at': get_current_timestamp(),
                'preview_only': self.preview_only,
                'progress': {
                    'total_users': 0,
                    'processed_users': 0,
                    'removed_users': 0,
                    'blocked_users': 0,
                    'deleted_accounts': 0,
                    'inaccessible_users': 0,
                    'errors': 0
                },
                'message': 'Initializing cleanup process...'
            })
            
            # Connect to database
            self.logger.info("Connecting to database...")
            if not await db_manager.connect():
                raise Exception("Failed to connect to database")
            
            await self._update_status({'status': 'connected', 'message': 'Connected to database'})
            
            # Create cleanup service
            cleanup_service = DatabaseCleanupService()
            
            if self.preview_only:
                # Run preview
                self.logger.info("Running cleanup preview...")
                await self._update_status({'status': 'running_preview', 'message': 'Generating preview...'})
                
                result = await cleanup_service.get_cleanup_preview()
                
                await self._update_status({
                    'status': 'completed',
                    'completed_at': get_current_timestamp(),
                    'result': result,
                    'message': 'Preview completed successfully'
                })
                
            else:
                # Run full cleanup
                self.logger.info("Running full cleanup...")
                await self._update_status({'status': 'running_cleanup', 'message': 'Starting cleanup process...'})
                
                # Create progress callback
                async def progress_callback(stats):
                    await self._update_progress(stats)
                
                result = await cleanup_service.cleanup_inactive_users(progress_callback)
                
                await self._update_status({
                    'status': 'completed',
                    'completed_at': get_current_timestamp(),
                    'result': result,
                    'message': 'Cleanup completed successfully'
                })
            
            self.logger.info(f"Background cleanup process completed: {self.cleanup_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in background cleanup process: {e}")
            await self._update_status({
                'status': 'failed',
                'completed_at': get_current_timestamp(),
                'error': str(e),
                'message': f'Cleanup failed: {str(e)}'
            })
            raise
        finally:
            # Disconnect from database
            await db_manager.disconnect()
    
    async def _update_status(self, updates: dict):
        """Update the status file with current progress"""
        try:
            # Read current status
            current_status = {}
            if self.status_file.exists():
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    current_status = json.load(f)
            
            # Update with new data
            current_status.update(updates)
            current_status['updated_at'] = get_current_timestamp()
            
            # Write back to file
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(current_status, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"Error updating status file: {e}")
    
    async def _update_progress(self, stats: dict):
        """Update progress information"""
        try:
            progress_updates = {
                'progress': {
                    'total_users': stats.get('total_users', 0),
                    'processed_users': stats.get('processed_users', 0),
                    'removed_users': len(stats.get('inactive_user_ids', [])),
                    'blocked_users': stats.get('blocked_users', 0),
                    'deleted_accounts': stats.get('deleted_accounts', 0),
                    'inaccessible_users': stats.get('inaccessible_users', 0),
                    'errors': stats.get('errors', 0)
                }
            }
            
            # Calculate percentage
            total = stats.get('total_users', 0)
            processed = stats.get('processed_users', 0)
            if total > 0:
                progress_updates['progress']['percentage'] = round((processed / total) * 100, 2)
            
            await self._update_status(progress_updates)
            
            # Log progress every 100 users
            if processed % 100 == 0:
                self.logger.info(f"Progress: {processed}/{total} users processed ({progress_updates['progress'].get('percentage', 0):.1f}%)")
                
        except Exception as e:
            self.logger.error(f"Error updating progress: {e}")

async def main():
    """Main function for the background cleanup process"""
    parser = argparse.ArgumentParser(description='Background Database Cleanup Process')
    parser.add_argument('--cleanup-id', required=True, help='Unique cleanup process ID')
    parser.add_argument('--admin-id', type=int, required=True, help='Admin user ID who initiated the cleanup')
    parser.add_argument('--preview-only', action='store_true', help='Run preview only, do not perform actual cleanup')
    
    args = parser.parse_args()
    
    # Create and run the background process
    process = BackgroundCleanupProcess(
        cleanup_id=args.cleanup_id,
        admin_id=args.admin_id,
        preview_only=args.preview_only
    )
    
    try:
        result = await process.run()
        print(f"✅ Background cleanup completed successfully: {args.cleanup_id}")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Background cleanup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Background cleanup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Fatal error in background cleanup: {e}")
        sys.exit(1)

# Database Cleanup Feature

## Overview

The Database Cleanup feature helps administrators identify and remove inactive users from the bot database. This helps maintain database performance and removes users who are no longer accessible.

## Features

### User Types Identified for Removal

1. **Blocked Users**: Users who have blocked the bot
2. **Deleted Accounts**: Users who have deleted their Telegram accounts  
3. **Inaccessible Users**: Users whose accounts are no longer accessible for other reasons

### Safety Features

- **Preview Mode**: See estimated cleanup results before executing
- **Rate Limiting**: Respects Telegram API limits (60 requests/second max)
- **Error Handling**: Continues processing even if some user checks fail
- **Data Integrity**: Preserves referral relationships and withdrawal records for audit purposes
- **Progress Tracking**: Real-time progress updates during cleanup

## How to Use

### Method 1: Admin Command

1. Send `/clean` command in the bot
2. Choose from the cleanup menu options
3. Use "Preview Cleanup" to see estimates
4. Use "Start Cleanup" to begin the process

### Method 2: Admin Panel

1. Access the admin panel with `/admin`
2. Click "🗑️ Database Cleanup"
3. Follow the same process as above

## Cleanup Process

### Step 1: Preview (Recommended)

- Analyzes a sample of users (up to 100)
- Provides estimates for total cleanup
- Shows percentage of inactive users
- No actual removal occurs

### Step 2: Confirmation

- Shows warning about permanent removal
- Requires explicit confirmation
- Cannot be undone once started

### Step 3: Execution

- Processes all users in batches
- Shows real-time progress updates
- Handles rate limiting automatically
- Provides final statistics

## Technical Details

### Rate Limiting

- Maximum 60 API calls per second to Telegram
- Automatic delays between requests
- Batch processing to optimize performance

### Data Handling

**Removed Data:**
- User records from users collection
- Task submissions
- Session data
- Rate limit records

**Preserved Data:**
- Withdrawal records (for audit trail)
- Referral relationships (to maintain data integrity)

### Error Handling

- Continues processing if individual user checks fail
- Logs all errors for admin review
- Provides error count in final statistics
- Safe defaults (don't remove on uncertain errors)

## Performance Considerations

### For Large Databases

- Process runs in background
- Batched processing prevents memory issues
- Progress updates every 50 users to avoid spam
- Automatic cleanup of related data

### Estimated Time

- ~1 second per user (due to rate limiting)
- 1,000 users ≈ 17 minutes
- 10,000 users ≈ 3 hours
- Progress is shown throughout

## Safety Recommendations

1. **Always run Preview first** to understand impact
2. **Backup your database** before major cleanups
3. **Run during low-traffic periods** to minimize disruption
4. **Monitor the process** through progress updates
5. **Check logs** after completion for any errors

## Example Output

### Preview Results
```
📊 Database Cleanup Preview

📈 Total Users: 5,247
🔍 Sample Checked: 100
🗑️ Estimated Removals: 342
📊 Estimated Percentage: 6.5%
```

### Final Results
```
✅ Database Cleanup Completed!

📊 Total Users Processed: 5,247
🗑️ Users Removed: 338
🚫 Blocked Bot: 201
❌ Deleted Accounts: 89
🔒 Inaccessible: 48
⚠️ Errors: 12

💾 Database space freed up by removing inactive users.
```

## Troubleshooting

### Common Issues

1. **High Error Count**: Check bot token and permissions
2. **Slow Progress**: Normal due to rate limiting
3. **Preview Shows 0%**: Database may be very clean already
4. **Connection Errors**: Check MongoDB connection

### When to Use

- **Regular Maintenance**: Monthly or quarterly
- **Performance Issues**: When database feels slow
- **Storage Optimization**: To free up space
- **Data Quality**: To maintain accurate user counts

## Security Notes

- Only admins can access this feature
- All actions are logged
- Irreversible operation - use with caution
- Preserves audit trail data

## Testing

Run the test script to validate functionality:

```bash
cd python
python test_cleanup.py
```

This will test the cleanup service without actually removing any users.
